import { Router } from 'express';
import { validate } from 'express-validation';
import controller from '../../controllers/auth.controller';
import validation from '../../validations/auth.validation';
import Auth from '../../../../middlewares/auth';

const router = Router();
router.route('/register').post(validate(validation.register), controller.register);
router.route('/login').post(controller.login);
router.route('/active/:id').patch(controller.active);
router.route('/change-password').post(Auth.authorize(), controller.changePassword);
router.route('/forgot-password').post(controller.forgotPassword);
router.route('/set-new-password').post(controller.setNewPassword);
router
  .route('/send-active-code')
  .post(validate(validation.sendActiveCode), Auth.authorize(), controller.sendActiveCode);

export default router;
