import { Schema, model, Document, Model, Types } from 'mongoose';
import { omitBy, isNil } from 'lodash';
import bcrypt from 'bcryptjs';
import moment from 'moment-timezone';
import httpStatus from 'http-status';
import APIError from '../../../utils/APIError';
import Vars from '../../../../config/vars';
import jwt from 'jwt-simple';
import { ExtractJwt } from 'passport-jwt';
import CustomerInfo from './customer.info.model';
import CustomerInfoOption from './customer.info.option.model';

const roles = ['admin', 'user', 'customer'];
const genders = ['male', 'female', 'other'];

interface IUser {
  user_name: string;
  email: string;
  password: string;
  password_default: string;
  name: string;
  type: string;
  gender: string;
  dob: any;
  phone: string;
  address: string;
  about: string;
  is_active: boolean;
  role: string;
  avatar: string;
  active_code: number;
  recovery_code: number;
  dial_code: string;
  info: Types.ObjectId;
}

interface IUserDocument extends IUser, Document {
  comparePassword(password: string, cb: any): string;
  passwordMatches(password: string): Promise<boolean>;
  transform(): {};
  token(): any;
}

interface IUserModel extends Model<IUserDocument> {
  generatePassword(): string;
  genders(): [];
  roles(): [];
  register({ phone, password }: { phone: string; password: string }): any;
  roles(): string[];
  findAndGenerateToken({
    phone,
    password,
    refreshObject,
  }: {
    phone: string;
    password: string;
    refreshObject: any;
  }): any;
  checkDuplicateEmail(error: any): any;
  get(id: string): any;
  getActivatingUser({ id, active_code }: { id: string; active_code: number }): any;
  list({
    page,
    perPage,
    sort,
    name,
    email,
    phone,
    role,
    user_name,
    education_type,
  }: {
    page?: number;
    perPage?: number;
    sort?: string;
    name?: string;
    email?: string;
    role?: string;
    phone?: string;
    user_name?: string;
    education_type?: string;
  }): any;
  countItem({
    name,
    email,
    phone,
    role,
    user_name,
  }: {
    name?: string;
    email?: string;
    role?: string;
    phone?: string;
    user_name?: string;
  }): any;
  getOneBy(body: {}): any;
  checkLogin(req: any): any;
  listForExport(): any;
}

const userSchema = new Schema<IUserDocument>(
  {
    phone: {
      type: String,
      trim: true,
      // unique: true,
      sparse: true,
      default: null,
    },
    email: {
      type: String,
      match: /^\S+@\S+\.\S+$/,
      // unique: true,
      trim: true,
      lowercase: true,
      sparse: true,
      default: null,
    },
    password: {
      type: String,
      required: true,
      minlength: 6,
      maxlength: 128,
    },
    password_default: {
      type: String,
      minlength: 8,
      maxlength: 8,
    },
    name: {
      type: String,
      maxlength: 128,
      index: true,
      trim: true,
      default: '',
    },
    user_name: {
      // unique: true,
      type: String,
    },
    type: {
      type: String,
      default: 'local',
    },
    gender: {
      type: String,
      default: 'other',
      enum: genders,
    },
    dob: {
      type: Date,
      default: '1500-01-01',
    },
    address: {
      type: String,
      trim: true,
      default: '',
    },
    about: {
      type: String,
      trim: true,
      default: '',
    },
    is_active: {
      type: Boolean,
      default: true,
    },
    active_code: {
      type: Number,
    },
    recovery_code: {
      type: Number,
    },
    role: {
      type: String,
      default: 'user',
      enum: roles,
    },
    avatar: {
      type: String,
      trim: true,
      default: '',
    },
    dial_code: {
      type: String,
      default: '+84',
    },
    info: {
      type: Schema.Types.ObjectId,
      ref: 'CustomerInfo',
    },
  },
  {
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  }
);

userSchema.statics.roles = () => roles;

// Compares the user's password with the request password
userSchema.methods.comparePassword = function (requestPassword: string, cb: any): any {
  bcrypt.compare(requestPassword, this.password, (err, isMatch) => {
    return cb(err, isMatch);
  });
};

userSchema.methods.passwordMatches = async function (password: string): Promise<boolean> {
  return await bcrypt.compare(password, this.password);
};

userSchema.methods.token = function (): any {
  const playload = {
    exp: moment().add(Vars.config().jwtExpirationInterval, 'minutes').unix(),
    iat: moment().unix(),
    sub: this._id,
  };
  return jwt.encode(playload, Vars.config().jwtSecret);
};

userSchema.methods.transform = function (): {} {
  const transformed: { [index: string]: any } = {};
  const fields = [
    '_id',
    'name',
    'user_name',
    'user_type',
    'about',
    'email',
    'address',
    'phone',
    'type',
    'gender',
    'dob',
    'avatar',
    'role',
    'info',
    // 'password_default',
    'is_active',
    'dial_code',
    'created_at',
  ];

  fields.forEach((field: any) => {
    transformed[field] = this[field];
  });

  return transformed;
};

userSchema.statics.genders = () => genders;
userSchema.statics.roles = () => roles;

userSchema.statics.register = ({ email, password }: { email: string; password: string }) => {
  const user = new User({ email, password });
  user.save();
  return user;
};

userSchema.statics.findAndGenerateToken = async function ({
  email,
  password,
  refreshObject,
  phone,
}: {
  email: string;
  password: string;
  refreshObject: any;
  phone: string;
}) {
  if (email) {
    if (email.indexOf('@') > -1) {
      const user = await this.findOne({ email: email }).exec();
      const err = {
        status: httpStatus.BAD_REQUEST,
        isPublic: true,
        message: '',
      };
      if (password) {
        if (user && (await user.passwordMatches(password))) {
          return { user, accessToken: user.token() };
        }
        err.message = 'Incorrect email or password';
      } else if (refreshObject && refreshObject.userPhone === email) {
        return { user, accessToken: user.token() };
      } else {
        err.message = 'Incorrect email or refreshToken';
      }
      throw new APIError(err);
    } else {
      const user = await this.findOne({ email }).exec();
      const err = {
        status: httpStatus.BAD_REQUEST,
        isPublic: true,
        message: '',
      };
      if (password) {
        if (user && (await user.passwordMatches(password))) {
          return { user, accessToken: user.token() };
        }
        err.message = 'Incorrect email or password';
      } else if (refreshObject && refreshObject.userPhone === email) {
        return { user, accessToken: user.token() };
      } else {
        err.message = 'Incorrect email or refreshToken';
      }
      throw new APIError(err);
    }
  } else if (phone) {
  }
};

userSchema.statics.checkDuplicateEmail = function (error: any) {
  if (
    (error.name === 'MongoError' && error.code === 11000) ||
    (error.name === 'MongoServerError' && error.code === 11000)
  ) {
    return new APIError({
      message: 'Validation Error',
      errors: [
        {
          field: 'phone or email',
          location: 'body',
          messages: ['phone number or email already exists'],
        },
      ],
      status: httpStatus.CONFLICT,
      isPublic: true,
      stack: error.stack,
    });
  }
  return error;
};

userSchema.statics.get = async function (id: string) {
  try {
    let user;

    if (Types.ObjectId.isValid(id)) {
      user = await this.findById(id).exec();
    }
    if (user) {
      const info = await CustomerInfo.findOne({ user: user._id })
        .populate('cellular_network', '_id name value')
        .populate('channel', '_id name value')
        .populate('school', '_id name value')
        .populate('province', '_id name value')
        .populate('district', '_id name value')
        .populate('major_interest_most', '_id name value')
        .populate('city_interest', '_id name value')
        .populate('level_interest', '_id name value')
        .populate('data_source', '_id name value')
        .populate('know_channel', '_id name value')
        .populate('is_apply_ttu', '_id name value')
        .populate('is_open_house', '_id name value')
        .populate('is_uni_prep', '_id name value')
        .populate('fee_reduction_type', '_id name value')
        .populate('data_status', '_id name value')
        .populate('ttu_target', '_id name value')
        .populate('moet_target', '_id name value')
        .populate('why_ttu', '_id name value')
        .populate('why_not_ttu', '_id name value');
      if (info) {
        return { user, info };
      }
      return { user };
    }

    throw new APIError({
      message: 'User does not exist',
      status: httpStatus.NOT_FOUND,
    });
  } catch (error) {
    throw error;
  }
};

userSchema.statics.getOneBy = async function (body: {}) {
  try {
    const user = await this.findOne(body).exec();
    if (user) {
      return user;
    }
    throw new APIError({
      message: 'User does not exist',
      status: httpStatus.NOT_FOUND,
    });
  } catch (error) {
    throw error;
  }
};

// lấy user để active
userSchema.statics.getActivatingUser = async function ({
  id,
  active_code,
}: {
  id: string;
  active_code: number;
}) {
  try {
    let user;

    if (Types.ObjectId.isValid(id)) {
      user = await this.findOne({ _id: id, active_code }).exec();
    }
    if (user) {
      return user;
    }

    throw new APIError({
      message: 'User or active code is wrong',
      status: httpStatus.NOT_FOUND,
    });
  } catch (error) {
    throw error;
  }
};

userSchema.statics.listOld = async function ({
  page = 1,
  perPage = 30,
  sort,
  name,
  email,
  phone,
  role,
  advisor,
  is_active = true,
  education_type,
  is_create_document,
  gender,
}: {
  page: number;
  perPage: number;
  sort: string;
  name: string;
  email: string;
  role: string;
  phone: string;
  advisor: string;
  is_active: boolean;
  education_type: string;
  is_create_document: string;
  gender: string;
}) {
  try {
    page = Number(page);
    perPage = Number(perPage);
    const options = omitBy(
      {
        name: new RegExp(name || '', 'i'),
        email,
        role,
        phone,
        is_active,
        'info.education_type': education_type,
        'info.advisor': advisor,
        gender,
      },
      isNil
    );
    if (is_create_document === 'true') {
      options['info.is_create_document'] = true;
    } else if (is_create_document === 'false') {
      options['info.is_create_document'] = false;
    }
    const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };
    if (education_type || advisor || is_create_document) {
      const result = await this.aggregate([
        {
          $lookup: {
            from: 'customerinfos',
            localField: 'info',
            foreignField: '_id',
            as: 'info',
          },
        },
        { $unwind: '$info' },
        { $match: options },
        { $sort: sortOpts || { created_at: -1 } },
        { $skip: (page - 1) * perPage },
        { $limit: perPage },
        {
          $project: {
            _id: 1,
            name: 1,
            email: 1,
            phone: 1,
            role: 1,
            avatar: 1,
            is_active: 1,
            created_at: 1,
            'info._id': 1,
            'info.education_type': 1,
            'info.advisor': 1,
            'info.is_create_document': 1,
          },
        },
      ]);
      return result;
    } else {
      const result = this.find(options)
        .populate('info', '_id is_create_document education_type advisor')
        .sort(sortOpts);
      if (perPage > -1) {
        result.skip(perPage * (page - 1)).limit(perPage);
      }
      return result.exec();
    }
  } catch (error) {
    throw error;
  }
};

userSchema.statics.list = async function ({
  page = 1,
  perPage = 30,
  sort,
  name,
  email,
  phone,
  role,
  is_active = true,
  advisor,
  education_type,
  is_create_document,
  gender,
}: {
  page: number;
  perPage: number;
  sort: string;
  name: string;
  email: string;
  role: string;
  phone: string;
  advisor: string;
  is_active: boolean;
  education_type: string;
  is_create_document: string;
  gender: string;
}) {
  try {
    page = Number(page);
    perPage = Number(perPage);
    const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };
    const options = omitBy(
      {
        name: new RegExp(name || '', 'i'),
        email,
        role,
        phone,
        is_active,
        gender,
      },
      isNil
    );
    const optionsInfo = omitBy(
      {
        education_type,
        advisor,
        is_create_document,
      },
      isNil
    );
    const result = this.find(options)
      .populate({
        path: 'info',
        select: '_id is_create_document education_type advisor',
        match: optionsInfo,
      })
      .sort(sortOpts);
    if (perPage > -1) {
      result.skip(perPage * (page - 1)).limit(perPage);
    }
    return result.exec();
  } catch (error) {
    throw error;
  }
};

userSchema.statics.countItem = async function ({
  name,
  email,
  phone,
  role,
  advisor,
  is_active = true,
  education_type,
  is_create_document,
  gender,
}: {
  name: string;
  email: string;
  role: string;
  phone: string;
  advisor: string;
  is_active: boolean;
  education_type: string;
  is_create_document: string;
  gender: string;
}) {
  try {
    // Sử dụng cùng logic với hàm list để đảm bảo consistency
    const options = omitBy(
      {
        name: new RegExp(name || '', 'i'),
        email,
        role,
        phone,
        is_active,
        gender,
      },
      isNil
    );

    const optionsInfo = omitBy(
      {
        education_type,
        advisor,
        is_create_document,
      },
      isNil
    );

    // Nếu có filter theo info fields, sử dụng aggregation để đồng bộ với populate logic
    if (advisor || education_type || is_create_document) {
      const pipeline = [
        // Match user fields trước
        { $match: options },

        // Lookup info
        {
          $lookup: {
            from: 'customerinfos',
            localField: 'info',
            foreignField: '_id',
            as: 'info_detail',
          },
        },

        // Unwind để có thể filter
        {
          $unwind: {
            path: '$info_detail',
            preserveNullAndEmptyArrays: true,
          },
        },
      ];

      // Build match conditions cho info fields
      const infoMatchConditions = {};

      if (education_type) {
        infoMatchConditions['info_detail.education_type'] = education_type;
      }

      if (advisor) {
        infoMatchConditions['info_detail.advisor'] = advisor;
      }

      if (is_create_document === 'true') {
        infoMatchConditions['info_detail.is_create_document'] = true;
      } else if (is_create_document === 'false') {
        infoMatchConditions['info_detail.is_create_document'] = false;
      }

      // Thêm match stage nếu có conditions
      if (Object.keys(infoMatchConditions).length > 0) {
        pipeline.push({ $match: infoMatchConditions });
      }

      // Count
      pipeline.push({ $count: 'totalCount' });

      const count = await this.aggregate(pipeline);
      return count.length > 0 ? count[0].totalCount : 0;
    } else {
      // Nếu không có info filters, chỉ count users thông thường
      return this.find(options).countDocuments();
    }
  } catch (error) {
    throw error;
  }
};

userSchema.statics.listForExport = async function () {
  try {
    const result = await this.aggregate([
      {
        $lookup: {
          from: 'customerinfos',
          localField: 'info',
          foreignField: '_id',
          as: 'info_detail',
        },
      },
      {
        $unwind: '$info_detail', // vì mỗi user chỉ có 1 info
      },
      {
        $match: {
          'info_detail.education_type': {
            $in: CustomerInfo.objectOpts(),
          },
        },
      },
      {
        $project: {
          _id: 1,
          email: 1,
          phone: 1,
          name: 1,
          gender: 1,
          dob: 1,
          created_at: 1,
          'info_detail.identify': 1,
          'info_detail.education_type': 1,
        },
      },
    ]);
    return result;
  } catch (error) {
    throw error;
  }
};
userSchema.statics.checkLogin = async function (req: any) {
  try {
    let userInfo: any = false;
    const token = ExtractJwt.fromAuthHeaderAsBearerToken()(req);
    if (token) {
      const decode = jwt.decode(token, Vars.config().jwtSecret);
      userInfo = await this.findById(decode.sub).exec();
    }
  } catch (error) {
    throw error;
  }
};

userSchema.statics.generatePassword = function (length = 8) {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  const charactersLength = characters.length;

  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }

  return result;
};

// Password hash middleware
userSchema.pre<IUserDocument>('save', async function (next) {
  const user = this;

  if (user.isModified('phone')) {
    const info = await CustomerInfo.findOne({ user: user._id });
    if (info) {
      const cellular_network_arr = await CustomerInfoOption.list({
        perPage: -1,
        key: 'cellular_network',
      });
      for (let i = 0; i < cellular_network_arr.length; i += 1) {
        const element = cellular_network_arr[i];
        if (element.note.includes(user.phone.slice(0, 3))) {
          const infoUpdated = Object.assign(info, { cellular_network: element._id });
          await infoUpdated.save();
        }
      }
    }
  }

  // Xử lý khi mật khẩu bị thay đổi
  if (!user.isModified('password')) {
    return next();
  }

  try {
    const salt = await bcrypt.genSalt(10);
    user.password = await bcrypt.hash(user.password, salt);
    return next();
  } catch (error: any) {
    return next(error);
  }
});

userSchema.post<IUserDocument>('save', async function (doc, next) {
  try {
    const info = await CustomerInfo.count({ user: doc._id });
    if (info < 1) {
      await CustomerInfo.create({ user: doc._id });
    }
    next();
  } catch (error: any) {
    return next(error);
  }
});

const User = model<IUserDocument, IUserModel>('User', userSchema);

export default User;
