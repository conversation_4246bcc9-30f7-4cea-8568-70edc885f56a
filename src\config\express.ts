import express from 'express';
import cors from 'cors';
import compress from 'compression';
import morgan from 'morgan';
import bodyParser from 'body-parser';
import methodOverride from 'method-override';
import helmet from 'helmet';
import Vars from './vars';
import routes from '../api/routes/v1';
import ErrorHand<PERSON> from '../api/middlewares/error';
// import passport from 'passport';
// import { jwt } from './passport';
import Passport from '../config/passport';

class Express {
  /**
   * Create the express object
   */
  public express: express.Application;

  /**
   * Initializes the express server
   */
  constructor() {
    this.express = express();

    // this.mountDotEnv();
    // this.mountMiddlewares();
    // this.mountRoutes();
  }

  /**
   * Starts the express server
   */
  public init(): any {
    const port: number = Vars.config().port;
    // Registering Exception / Error Handlers
    // this.express.use(ExceptionHandler.logErrors);
    // this.express.use(ExceptionHandler.clientErrorHandler);
    // this.express.use(ExceptionHandler.errorHandler);
    // this.express = ExceptionHandler.notFoundHandler(this.express);

    // enable CORS - Cross Origin Resource Sharing
    this.express.use(
      cors({
        origin: '*',
        methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
        preflightContinue: false,
        optionsSuccessStatus: 204,
      })
    );

    // gzip compression
    this.express.use(compress());

    // request logging. dev: console | production: file
    this.express.use(morgan(Vars.config().logs));

    // // parse body params and attache them to req.body
    this.express.use(bodyParser.json());
    this.express.use(bodyParser.urlencoded({ extended: true }));

    // lets you use HTTP verbs such as PUT or DELETE
    // in places where the client doesn't support it
    this.express.use(methodOverride());

    // secure apps by setting various HTTP headers
    this.express.use(helmet({ crossOriginResourcePolicy: false }));
    // this.express.use(helmet());

    this.express.use('/v1', routes);

    // if error is not an instanceOf APIError, convert it.
    this.express.use(ErrorHandler.converter);

    // catch 404 and forward to error handler
    this.express.use(ErrorHandler.notFound);

    // error handler, send stacktrace only during development
    this.express.use(ErrorHandler.handler);

    Passport.mountPackage(this.express);

    // enable authentication
    // this.express.use(passport.initialize());
    // passport.use('jwt', jwt);

    // Start the server on the specified port
    this.express
      .listen(port, () => {
        return console.info('\x1b[33m%s\x1b[0m', `Server :: Running @ 'http://localhost:${port}'`);
      })
      .on('error', (_error) => {
        return console.log('Error: ', _error.message);
      });
  }
}

/** Export the express module */
export default new Express();
