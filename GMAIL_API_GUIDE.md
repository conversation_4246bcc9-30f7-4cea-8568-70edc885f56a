# Gmail API Service Guide

## Tổng quan
`GmailService` là một service sử dụng Gmail API trực tiếp thay vì nodemailer để gửi email. Service này cung cấp nhiều tính năng mạnh mẽ và hiệu suất tốt hơn.

## Tính năng chính

### 1. **Direct Gmail API Integration**
- Sử dụng Gmail API v1 trực tiếp
- Không cần nodemailer middleware
- Hi<PERSON>u suất cao và ít dependency

### 2. **Singleton Pattern**
- <PERSON><PERSON><PERSON> bảo chỉ có một instance duy nhất
- Tự động khởi tạo OAuth2 client

### 3. **Flexible Email Options**
- Support TO, CC, BCC
- HTML content
- Attachments (future feature)
- Bulk email sending

## Cấu hình

### Environment Variables:
```env
MAILER_CLIENT_ID=your-gmail-client-id
MAILER_CLIENT_SECRET=your-gmail-client-secret
MAILER_REDIRECT_URI=https://developers.google.com/oauthplayground
MAILER_REFRESH_TOKEN=your-gmail-refresh-token
MAILER_EMAIL=<EMAIL>
MAILER_APPLY_NOTIFICATION_ADDRESS=["<EMAIL>"]
```

### Setup OAuth2:
1. Tạo project trong Google Cloud Console
2. Enable Gmail API
3. Tạo OAuth2 credentials
4. Sử dụng OAuth2 Playground để lấy refresh token

## Cách sử dụng

### 1. **Import và sử dụng cơ bản**
```typescript
import GmailService from '../config/gmail';

// Service đã được khởi tạo sẵn (singleton)
const gmailService = GmailService;
```

### 2. **Gửi email đơn giản**
```typescript
await GmailService.sendSimpleEmail(
  '<EMAIL>',
  'Subject here',
  '<h1>Hello!</h1><p>This is HTML content</p>'
);
```

### 3. **Gửi email với options đầy đủ**
```typescript
await GmailService.sendEmail({
  to: '<EMAIL>',
  cc: '<EMAIL>',
  bcc: '<EMAIL>',
  subject: 'Subject here',
  html: '<h1>Hello!</h1><p>HTML content</p>',
  from: 'Custom Sender <<EMAIL>>' // Optional
});
```

### 4. **Gửi email hàng loạt**
```typescript
const emails = [
  {
    to: '<EMAIL>',
    subject: 'Subject 1',
    html: '<p>Content 1</p>'
  },
  {
    to: '<EMAIL>',
    subject: 'Subject 2',
    html: '<p>Content 2</p>'
  }
];

const results = await GmailService.sendBulkEmails(emails);
console.log(results); // Array of results with success/failure status
```

### 5. **Gửi thông báo đăng ký**
```typescript
await GmailService.sendApplyNotification({
  email: '<EMAIL>',
  name: 'Nguyễn Văn A',
  phone: '0123456789',
  education_type: 'Đại học',
  major_interest_most: 'Công nghệ thông tin',
  now: new Date().toISOString()
});
```

### 6. **Gửi thông báo advisor**
```typescript
await GmailService.sendAdvisorNotification({
  email: '<EMAIL>',
  name: 'Trần Thị B',
  phone: '0987654321',
  advisor_name: 'Thầy Nguyễn Văn C'
});
```

## API Methods

### Core Methods:
- `sendEmail(options)`: Gửi email với options đầy đủ
- `sendSimpleEmail(to, subject, html)`: Gửi email đơn giản
- `sendBulkEmails(emails)`: Gửi email hàng loạt

### Template Methods:
- `sendApplyNotification(data)`: Gửi thông báo đăng ký
- `sendAdvisorNotification(data)`: Gửi thông báo advisor

### Utility Methods:
- `testConnection()`: Kiểm tra kết nối Gmail API
- `getProfile()`: Lấy thông tin profile Gmail
- `getLabels()`: Lấy danh sách labels
- `updateConfig(newConfig)`: Cập nhật cấu hình

## So sánh với Nodemailer

| Tính năng | Gmail API | Nodemailer |
|-----------|-----------|------------|
| Performance | ⚡ Cao | 🐌 Trung bình |
| Dependencies | 📦 Ít | 📦 Nhiều |
| Rate Limiting | 🚀 Gmail limits | 🚀 Gmail limits |
| Error Handling | ✅ Chi tiết | ⚠️ Cơ bản |
| Bulk Sending | ✅ Tối ưu | ❌ Chậm |
| API Features | ✅ Đầy đủ | ❌ Hạn chế |

## Error Handling

### 1. **Connection Errors**
```typescript
try {
  const isConnected = await GmailService.testConnection();
  if (!isConnected) {
    console.log('Gmail API connection failed');
  }
} catch (error) {
  console.error('Connection error:', error);
}
```

### 2. **Send Email Errors**
```typescript
try {
  await GmailService.sendEmail(emailOptions);
} catch (error) {
  if (error.code === 403) {
    console.log('Insufficient permissions');
  } else if (error.code === 400) {
    console.log('Invalid email format');
  } else {
    console.log('Other error:', error.message);
  }
}
```

### 3. **Bulk Email Error Handling**
```typescript
const results = await GmailService.sendBulkEmails(emails);
results.forEach(result => {
  if (result.success) {
    console.log(`✅ Sent to ${result.to}: ${result.messageId}`);
  } else {
    console.log(`❌ Failed to ${result.to}: ${result.error}`);
  }
});
```

## Testing

### Chạy test:
```bash
# Compile TypeScript trước
npm run build

# Chạy test
node test-gmail-api.js
```

### Test cases:
- ✅ Connection test
- ✅ Profile retrieval
- ✅ Labels retrieval
- ✅ Simple email
- ✅ Email with CC/BCC
- ✅ Bulk emails
- ✅ Apply notification
- ✅ Advisor notification

## Migration từ Nodemailer

### 1. **Thay thế import**
```typescript
// Cũ
import Nodemailer from '../config/mailer';

// Mới
import GmailService from '../config/gmail';
```

### 2. **Thay thế method calls**
```typescript
// Cũ
await Nodemailer.sendApplyNotification(data);

// Mới
await GmailService.sendApplyNotification(data);
```

### 3. **Cập nhật error handling**
```typescript
// Mới có thêm detailed error information
try {
  await GmailService.sendEmail(options);
} catch (error) {
  console.log('Error code:', error.code);
  console.log('Error message:', error.message);
}
```

## Best Practices

1. **Rate Limiting**: Gmail API có giới hạn 1 billion quota units/day
2. **Batch Processing**: Sử dụng `sendBulkEmails` cho nhiều email
3. **Error Handling**: Luôn wrap trong try-catch
4. **Testing**: Test với email thật trước khi deploy
5. **Monitoring**: Log message IDs để tracking

## Troubleshooting

### 1. **"Invalid credentials" error**
- Kiểm tra CLIENT_ID, CLIENT_SECRET
- Verify refresh token chưa expire
- Đảm bảo Gmail API được enable

### 2. **"Insufficient permissions" error**
- Kiểm tra OAuth2 scopes
- Verify email account có quyền gửi

### 3. **"Quota exceeded" error**
- Kiểm tra Gmail API quota
- Implement rate limiting
- Sử dụng exponential backoff

### 4. **"Invalid email format" error**
- Validate email addresses trước khi gửi
- Kiểm tra HTML content encoding
- Verify subject line không có ký tự đặc biệt

## Security Notes

- Không hardcode credentials trong code
- Sử dụng environment variables
- Rotate refresh tokens định kỳ
- Monitor unusual sending patterns
- Implement proper access controls
