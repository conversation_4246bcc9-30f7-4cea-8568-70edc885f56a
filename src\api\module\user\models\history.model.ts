// 28/2/2025
import APIError from '../../../utils/APIError';
import httpStatus from 'http-status';
import { Schema, model, Document, Model, Types } from 'mongoose';
import { omitBy, isNil } from 'lodash';

const tagOpts = [
  'Tiềm năng cao',
  'Tiềm năng thấp',
  'Mới tiếp nhận',
  'Thành công',
  'Không thành công',
];

interface IHistory {
  customer: Types.ObjectId; //khách hàng
  advisor: Types.ObjectId; //Tư vấn viên
  content: String;
  tag: String;

  is_active: boolean;
  created_by: Types.ObjectId;
  updated_by: Types.ObjectId;
  deleted_at: Date;
  deleted_by: Types.ObjectId;
}

interface IHistoryDocument extends IHistory, Document {
  transform(): {};
}

interface IHistoryModel extends Model<IHistoryDocument> {
  tagOpts(): string[];
  get(id: string): any;
  list({
    page,
    perPage,
    sort,
    customer,
    advisor,
    is_active,
  }: {
    page?: number;
    perPage?: number;
    sort?: string;
    customer?: string;
    advisor?: string;
    is_active?: boolean;
  }): any;
  countItem({
    customer,
    advisor,
    is_active,
  }: {
    customer?: string;
    advisor?: string;
    is_active?: boolean;
  }): any;
}

const historySchema = new Schema<IHistoryDocument>(
  {
    customer: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    advisor: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    content: {
      type: String,
      default: '',
    },
    tag: [
      {
        type: String,
        enum: tagOpts,
      },
    ],

    is_active: {
      type: Boolean,
      default: true,
    },
    created_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    updated_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    deleted_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    deleted_at: {
      type: Date,
    },
  },
  {
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  }
);

historySchema.methods.transform = function (): {} {
  const transformed: { [index: string]: any } = {};
  const fields = [
    '_id', //
    'customer',
    'advisor',
    'content',
    'tag',

    'is_active',
    'created_at',
    'is_active',
    'delete_at',
  ];

  fields.forEach((field: any) => {
    transformed[field] = this[field];
  });

  return transformed;
};

historySchema.statics.list = function ({
  page = 1,
  perPage = 30,
  sort,
  is_active = true,
  customer,
  advisor,
}: {
  page: number;
  perPage: number;
  sort: string;
  customer: string;
  advisor: string;
  is_active: boolean;
}) {
  try {
    const options = omitBy(
      {
        customer,
        advisor,
        is_active,
      },
      isNil
    );
    const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };
    const result = this.find(options)
    .populate('customer','_id name avatar')
    .populate('advisor','_id name avatar')
    .sort(sortOpts);
    if (perPage > -1) {
      result.skip(perPage * (page - 1)).limit(perPage);
    }
    return result.exec();
  } catch (error) {
    throw error;
  }
};

historySchema.statics.countItem = function ({
  customer,
  advisor,
  is_active = true,
}: {
  customer: string;
  advisor: string;
  is_active: boolean;
}) {
  try {
    const options = omitBy(
      {
        customer,
        advisor,
        is_active,
      },
      isNil
    );
    return this.find(options).count().exec();
  } catch (error) {
    throw error;
  }
};

historySchema.statics.get = async function (id: string) {
  try {
    const info = await this.findById(id).exec();
    if (info) {
      return info;
    }
    throw new APIError({
      message: 'History does not exist',
      status: httpStatus.NOT_FOUND,
    });
  } catch (error) {
    throw error;
  }
};

historySchema.statics.tagOpts = () => tagOpts;

const History = model<IHistoryDocument, IHistoryModel>('History', historySchema);

export default History;
