// // src/models/admission.results.model.ts

// import { DataTypes, Model, Optional } from 'sequelize';
// import { sequelize } from '../../../../config/sequelize'; // Đ<PERSON>ờng dẫn đến file khởi tạo Sequelize instance

// // Định nghĩa các thuộc tính của model
// interface IAdmissionResult {
//   id: number;
//   namtuyensinh: number;
//   cmnd: string;
//   ngaysinh: Date;
//   hedaotao: string;
//   mahoso: string;
//   hoten: string;
//   nganh: string;
//   chuongtrinh: number;
//   diemtb: number;
//   kqht: number;
//   approve_kq_co_dk: number;
// }

// // Các trường có thể bỏ qua khi tạo bản ghi mới
// type AdmissionResultCreationAttributes = Optional<IAdmissionResult, 'id'>;

// // Khai báo class model
// export class AdmissionResult
//   extends Model<IAdmissionResult, AdmissionResultCreationAttributes>
//   implements IAdmissionResult
// {
//   public id!: number;
//   public namtuyensinh!: number;
//   public cmnd!: string;
//   public ngaysinh!: Date;
//   public hedaotao!: string;
//   public mahoso!: string;
//   public hoten!: string;
//   public nganh!: string;
//   public chuongtrinh!: number;
//   public diemtb!: number;
//   public kqht!: number;
//   public approve_kq_co_dk!: number;
// }

// // Khởi tạo model với Sequelize
// AdmissionResult.init(
//   {
//     id: {
//       type: DataTypes.INTEGER.UNSIGNED,
//       autoIncrement: true,
//       primaryKey: true,
//     },
//     namtuyensinh: {
//       type: DataTypes.INTEGER.UNSIGNED,
//       allowNull: false,
//     },
//     cmnd: {
//       type: DataTypes.STRING,
//       allowNull: false,
//     },
//   },
//   {
//     sequelize, // truyền Sequelize instance
//     tableName: 'ttu_trungtuyen',
//     modelName: 'AdmissionResult',
//     timestamps: false, // createdAt và updatedAt
//   }
// );
