// 16/5/2024
import { Request, Response, NextFunction } from 'express';
import Category from '../models/article.category.model';
import <PERSON><PERSON>r<PERSON><PERSON><PERSON> from '../../../middlewares/error';
import Success<PERSON>andler from '../../../middlewares/success';

class CategoryController {
  public static async load(req: any, res: Response, next: NextFunction, id: string) {
    try {
      const category = await Category.get(id);
      req.locals = { category };
      return next();
    } catch (error) {
      return ErrorHandler.handler(error, req, res);
    }
  }

  public static async get(req: any, res: Response, next: NextFunction) {
    try {
      SuccessHandler.success(req.locals.category.transform(), req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async remove(req: any, res: Response, next: NextFunction) {
    try {
      const { category } = req.locals;
      const now = new Date();

      const deleted = Object.assign(category, {
        deleted_by: req.user._id,
        deleted_at: now,
        is_active: false,
      });
      await deleted.save();
      SuccessHandler.success(deleted, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async list(req: Request, res: Response, next: NextFunction) {
    try {
      const categorys = await Category.list(req.query);
      const count = await Category.countItem(req.query);
      const transformed = categorys.map((category: any) => category.transform());
      SuccessHandler.success({ total: count, docs: transformed }, req, res);
    } catch (error: any) {
      next(error);
    }
  }

  public static async update(req: any, res: Response, next: NextFunction) {
    try {
      const { category } = req.locals;
      const { body, user } = req;
      const now = new Date();
      body.updated_by = user._id;
      body.updated_at = now;

      const updated = Object.assign(category, body);
      await updated.save();
      SuccessHandler.success(updated, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async create(req: any, res: Response, next: NextFunction) {
    try {
      const { body, user } = req;
      const now = new Date();
      body.created_at_by = user._id;
      body.created_at = now;
      const category = new Category(body);
      await category.save();
      SuccessHandler.success(category, req, res);
    } catch (error) {
      next(error);
    }
  }
}

export default CategoryController;
