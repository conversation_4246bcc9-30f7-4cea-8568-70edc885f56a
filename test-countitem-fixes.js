// Test script để kiểm tra các lỗi đã được sửa trong countItem function
const axios = require('axios');

// Cấu hình base URL
const BASE_URL = 'http://localhost:3000/api/v1/users';

// Mock token
const AUTH_TOKEN = 'your-admin-token-here';

const headers = {
  'Authorization': `Bearer ${AUTH_TOKEN}`,
  'Content-Type': 'application/json'
};

/**
 * Test ObjectId handling trong advisor field
 */
async function testAdvisorObjectIdHandling() {
  console.log('\n🔍 Test: Advisor ObjectId Handling');
  console.log('=' .repeat(50));

  const testCases = [
    {
      name: 'Valid ObjectId',
      advisor: '507f1f77bcf86cd799439011' // Valid ObjectId format
    },
    {
      name: 'Invalid ObjectId (should handle gracefully)',
      advisor: 'invalid-object-id'
    },
    {
      name: 'Short string',
      advisor: 'abc123'
    },
    {
      name: 'Empty string',
      advisor: ''
    },
    {
      name: 'Null value',
      advisor: null
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n📝 Testing: ${testCase.name}`);
    console.log(`   Advisor: ${testCase.advisor}`);

    try {
      const countResponse = await axios.get(`${BASE_URL}/count`, {
        headers,
        params: { advisor: testCase.advisor }
      });

      console.log(`   ✅ Count success: ${countResponse.data.data || countResponse.data}`);

      // Also test list to ensure consistency
      const listResponse = await axios.get(BASE_URL, {
        headers,
        params: { advisor: testCase.advisor, page: 1, perPage: 5 }
      });

      console.log(`   ✅ List success: ${listResponse.data.data?.length || 0} items`);

    } catch (error) {
      if (testCase.advisor === '' || testCase.advisor === null) {
        console.log(`   ✅ Expected behavior for empty/null advisor`);
      } else {
        console.log(`   ❌ Error: ${error.response?.data?.message || error.message}`);
      }
    }
  }
}

/**
 * Test type safety của infoMatchConditions
 */
async function testInfoMatchConditionsTypeSafety() {
  console.log('\n🔒 Test: InfoMatchConditions Type Safety');
  console.log('=' .repeat(50));

  const testCases = [
    {
      name: 'Boolean true',
      params: { is_create_document: 'true' }
    },
    {
      name: 'Boolean false',
      params: { is_create_document: 'false' }
    },
    {
      name: 'String education_type',
      params: { education_type: 'university' }
    },
    {
      name: 'Combined info fields',
      params: { 
        education_type: 'university',
        is_create_document: 'true',
        advisor: '507f1f77bcf86cd799439011'
      }
    },
    {
      name: 'Mixed valid and invalid',
      params: { 
        education_type: 'university',
        advisor: 'invalid-id',
        is_create_document: 'false'
      }
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n📝 Testing: ${testCase.name}`);
    console.log(`   Params: ${JSON.stringify(testCase.params)}`);

    try {
      const countResponse = await axios.get(`${BASE_URL}/count`, {
        headers,
        params: testCase.params
      });

      const count = countResponse.data.data || countResponse.data;
      console.log(`   ✅ Count: ${count}`);

      // Verify type safety by checking if count is a number
      if (typeof count === 'number' && count >= 0) {
        console.log(`   ✅ Type safety: Count is valid number`);
      } else {
        console.log(`   ⚠️  Type issue: Count is not a valid number`);
      }

    } catch (error) {
      console.log(`   ❌ Error: ${error.response?.data?.message || error.message}`);
    }
  }
}

/**
 * Test aggregation pipeline performance
 */
async function testAggregationPerformance() {
  console.log('\n⚡ Test: Aggregation Pipeline Performance');
  console.log('=' .repeat(50));

  const testCases = [
    {
      name: 'Simple user filter (no aggregation)',
      params: { role: 'customer' }
    },
    {
      name: 'Info filter (uses aggregation)',
      params: { education_type: 'university' }
    },
    {
      name: 'Complex aggregation',
      params: { 
        education_type: 'university',
        advisor: '507f1f77bcf86cd799439011',
        is_create_document: 'true'
      }
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n📊 Testing: ${testCase.name}`);

    const startTime = Date.now();
    
    try {
      const countResponse = await axios.get(`${BASE_URL}/count`, {
        headers,
        params: testCase.params
      });

      const endTime = Date.now();
      const duration = endTime - startTime;
      const count = countResponse.data.data || countResponse.data;

      console.log(`   ✅ Count: ${count} in ${duration}ms`);

      if (duration < 1000) {
        console.log(`   ✅ Performance: Good (${duration}ms)`);
      } else if (duration < 3000) {
        console.log(`   ⚠️  Performance: Acceptable (${duration}ms)`);
      } else {
        console.log(`   ❌ Performance: Slow (${duration}ms)`);
      }

    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      console.log(`   ❌ Error after ${duration}ms: ${error.message}`);
    }
  }
}

/**
 * Test edge cases và error handling
 */
async function testEdgeCasesAndErrorHandling() {
  console.log('\n🔍 Test: Edge Cases and Error Handling');
  console.log('=' .repeat(50));

  const edgeCases = [
    {
      name: 'All empty filters',
      params: { name: '', email: '', phone: '', advisor: '', education_type: '' }
    },
    {
      name: 'Special characters in name',
      params: { name: 'Nguyễn Văn A' }
    },
    {
      name: 'Very long advisor ID',
      params: { advisor: 'a'.repeat(100) }
    },
    {
      name: 'Invalid boolean string',
      params: { is_create_document: 'maybe' }
    },
    {
      name: 'Numeric strings',
      params: { education_type: '123', advisor: '456' }
    }
  ];

  for (const testCase of edgeCases) {
    console.log(`\n📝 Testing: ${testCase.name}`);

    try {
      const countResponse = await axios.get(`${BASE_URL}/count`, {
        headers,
        params: testCase.params
      });

      const count = countResponse.data.data || countResponse.data;
      console.log(`   ✅ Handled gracefully: ${count}`);

    } catch (error) {
      console.log(`   ⚠️  Error (may be expected): ${error.response?.data?.message || error.message}`);
    }
  }
}

/**
 * Test consistency với list function
 */
async function testConsistencyWithList() {
  console.log('\n🔄 Test: Consistency with List Function');
  console.log('=' .repeat(50));

  const testCases = [
    {
      name: 'Info filter consistency',
      params: { education_type: 'university' }
    },
    {
      name: 'Advisor filter consistency',
      params: { advisor: '507f1f77bcf86cd799439011' }
    },
    {
      name: 'Boolean filter consistency',
      params: { is_create_document: 'true' }
    },
    {
      name: 'Mixed filters consistency',
      params: { 
        name: 'test',
        education_type: 'university',
        is_create_document: 'false'
      }
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n📝 Testing: ${testCase.name}`);

    try {
      // Get count
      const countResponse = await axios.get(`${BASE_URL}/count`, {
        headers,
        params: testCase.params
      });

      // Get full list (with high perPage to get all items)
      const listResponse = await axios.get(BASE_URL, {
        headers,
        params: { ...testCase.params, page: 1, perPage: 1000 }
      });

      const count = countResponse.data.data || countResponse.data;
      const listLength = listResponse.data.data?.length || 0;

      console.log(`   📊 Count: ${count}, List length: ${listLength}`);

      if (count === listLength) {
        console.log(`   ✅ Perfect consistency`);
      } else if (count >= listLength) {
        console.log(`   ⚠️  Count higher than list (may be due to pagination or populate filtering)`);
      } else {
        console.log(`   ❌ Inconsistency detected: Count < List length`);
      }

    } catch (error) {
      console.log(`   ❌ Error: ${error.response?.data?.message || error.message}`);
    }
  }
}

/**
 * Chạy tất cả tests
 */
async function runAllTests() {
  console.log('🚀 CountItem Fixes Test Suite');
  console.log('=' .repeat(60));

  // Test 1: ObjectId handling
  await testAdvisorObjectIdHandling();

  // Test 2: Type safety
  await testInfoMatchConditionsTypeSafety();

  // Test 3: Performance
  await testAggregationPerformance();

  // Test 4: Edge cases
  await testEdgeCasesAndErrorHandling();

  // Test 5: Consistency
  await testConsistencyWithList();

  console.log('\n🎉 All countItem fix tests completed!');
  
  console.log('\n📋 Summary of Fixes:');
  console.log('   ✅ Added type annotation for infoMatchConditions');
  console.log('   ✅ Proper ObjectId handling for advisor field');
  console.log('   ✅ Graceful error handling for invalid ObjectIds');
  console.log('   ✅ Consistent boolean field processing');
  console.log('   ✅ Improved aggregation pipeline logic');

  console.log('\n💡 Key Improvements:');
  console.log('   - Type safety with proper annotations');
  console.log('   - ObjectId conversion with fallback');
  console.log('   - Consistent filter logic with list function');
  console.log('   - Better error handling');
  console.log('   - Performance optimization');
}

// Chạy tests
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testAdvisorObjectIdHandling,
  testInfoMatchConditionsTypeSafety,
  testAggregationPerformance,
  testEdgeCasesAndErrorHandling,
  testConsistencyWithList,
  runAllTests
};
