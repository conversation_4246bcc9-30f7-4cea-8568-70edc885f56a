import { google } from 'googleapis';
import Vars from './vars';
import ApplyNotificationTemplate from '../api/template/applyNotification.template';
import AdvisorNotificationTemplate from '../api/template/advisorNotification.template';

// Interface cho email options
interface EmailOptions {
  to: string | string[];
  subject: string;
  html: string;
  from?: string;
  cc?: string | string[];
  bcc?: string | string[];
  attachments?: Array<{
    filename: string;
    content: Buffer | string;
    contentType?: string;
  }>;
}

// Interface cho apply notification
interface ApplyNotificationData {
  email: string;
  name: string;
  phone: string;
  education_type: string;
  major_interest_most: string;
  now: string;
}

// Interface cho advisor notification
interface AdvisorNotificationData {
  email: string;
  name: string;
  phone: string;
  advisor_name: string;
}

class GmailService {
  private static instance: GmailService;
  private gmail: any;
  private oAuth2Client: any;
  private config: any;

  private constructor() {
    this.config = Vars.config().mail;
    this.initializeAuth();
  }

  public static getInstance(): GmailService {
    if (!GmailService.instance) {
      GmailService.instance = new GmailService();
    }
    return GmailService.instance;
  }

  private initializeAuth(): void {
    try {
      // Khởi tạo OAuth2Client
      this.oAuth2Client = new google.auth.OAuth2(
        this.config.clientId,
        this.config.clientSecret,
        this.config.redirectUri
      );

      // Set refresh token
      this.oAuth2Client.setCredentials({
        refresh_token: this.config.refreshToken,
      });

      // Khởi tạo Gmail API
      this.gmail = google.gmail({ version: 'v1', auth: this.oAuth2Client });

      console.log('✅ Gmail API initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Gmail API:', error);
      throw error;
    }
  }

  /**
   * Tạo raw email message từ email options
   */
  private createRawMessage(emailOptions: EmailOptions): string {
    const { to, subject, html, from, cc, bcc } = emailOptions;

    // Xử lý recipients
    const toAddresses = Array.isArray(to) ? to.join(', ') : to;
    const fromAddress = from || `Tan Tao University <${this.config.senderEmail}>`;

    // Tạo email headers
    let emailLines = [
      `From: ${fromAddress}`,
      `To: ${toAddresses}`,
      `Subject: ${subject}`,
      'MIME-Version: 1.0',
      'Content-Type: text/html; charset=utf-8',
    ];

    // Thêm CC nếu có
    if (cc) {
      const ccAddresses = Array.isArray(cc) ? cc.join(', ') : cc;
      emailLines.push(`Cc: ${ccAddresses}`);
    }

    // Thêm BCC nếu có
    if (bcc) {
      const bccAddresses = Array.isArray(bcc) ? bcc.join(', ') : bcc;
      emailLines.push(`Bcc: ${bccAddresses}`);
    }

    // Thêm empty line và body
    emailLines.push('');
    emailLines.push(html);

    // Tạo raw message và encode base64
    const email = emailLines.join('\r\n');
    return Buffer.from(email).toString('base64').replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
  }

  /**
   * Gửi email sử dụng Gmail API
   */
  public async sendEmail(emailOptions: EmailOptions): Promise<any> {
    try {
      const rawMessage = this.createRawMessage(emailOptions);

      const response = await this.gmail.users.messages.send({
        userId: 'me',
        requestBody: {
          raw: rawMessage,
        },
      });

      console.log('✅ Email sent successfully:', response.data.id);
      return response.data;
    } catch (error) {
      console.error('❌ Failed to send email:', error);
      throw error;
    }
  }

  /**
   * Gửi email với template đơn giản
   */
  public async sendSimpleEmail(
    to: string | string[],
    subject: string,
    htmlContent: string,
    options?: Partial<EmailOptions>
  ): Promise<any> {
    const emailOptions: EmailOptions = {
      to,
      subject,
      html: htmlContent,
      ...options,
    };

    return this.sendEmail(emailOptions);
  }

  /**
   * Gửi thông báo đăng ký xét tuyển
   */
  public async sendApplyNotification(data: ApplyNotificationData): Promise<any> {
    try {
      const { email, name, phone, education_type, major_interest_most, now } = data;

      const emailOptions: EmailOptions = {
        to: JSON.parse(this.config.applyNotificationAddress),
        subject: `CRM - Thông báo đăng ký xét tuyển mới #${name}`,
        html: ApplyNotificationTemplate.get({
          email,
          name,
          phone,
          education_type,
          now,
          major_interest_most,
        }),
      };

      const result = await this.sendEmail(emailOptions);
      console.log(`✅ Apply notification sent for: ${name}`);
      return result;
    } catch (error) {
      console.error('❌ Error sending apply notification:', error);
      throw error;
    }
  }

  /**
   * Gửi thông báo phân công advisor
   */
  public async sendAdvisorNotification(data: AdvisorNotificationData): Promise<any> {
    try {
      const { email, name, phone, advisor_name } = data;

      const emailOptions: EmailOptions = {
        to: email,
        subject: `CRM - Thông báo phân công chăm sóc sinh viên #${name}`,
        html: AdvisorNotificationTemplate.get({
          name,
          phone,
          advisor_name,
        }),
      };

      const result = await this.sendEmail(emailOptions);
      console.log(`✅ Advisor notification sent to: ${email}`);
      return result;
    } catch (error) {
      console.error('❌ Error sending advisor notification:', error);
      throw error;
    }
  }

  /**
   * Gửi email hàng loạt
   */
  public async sendBulkEmails(emails: EmailOptions[]): Promise<any[]> {
    const results = [];

    for (const emailOptions of emails) {
      try {
        const result = await this.sendEmail(emailOptions);
        results.push({ success: true, messageId: result.id, to: emailOptions.to });

        // Delay nhỏ giữa các email để tránh rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.error(`❌ Failed to send email to ${emailOptions.to}:`, error);
        results.push({
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          to: emailOptions.to
        });
      }
    }

    return results;
  }

  /**
   * Kiểm tra kết nối Gmail API
   */
  public async testConnection(): Promise<boolean> {
    try {
      const response = await this.gmail.users.getProfile({
        userId: 'me',
      });

      console.log(`✅ Gmail API connected. Email: ${response.data.emailAddress}`);
      return true;
    } catch (error) {
      console.error('❌ Gmail API connection failed:', error);
      return false;
    }
  }

  /**
   * Lấy thông tin profile Gmail
   */
  public async getProfile(): Promise<any> {
    try {
      const response = await this.gmail.users.getProfile({
        userId: 'me',
      });

      return {
        emailAddress: response.data.emailAddress,
        messagesTotal: response.data.messagesTotal,
        threadsTotal: response.data.threadsTotal,
        historyId: response.data.historyId,
      };
    } catch (error) {
      console.error('❌ Failed to get Gmail profile:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách labels
   */
  public async getLabels(): Promise<any[]> {
    try {
      const response = await this.gmail.users.labels.list({
        userId: 'me',
      });

      return response.data.labels || [];
    } catch (error) {
      console.error('❌ Failed to get Gmail labels:', error);
      throw error;
    }
  }

  /**
   * Cập nhật cấu hình OAuth
   */
  public updateConfig(newConfig: Partial<typeof this.config>): void {
    this.config = { ...this.config, ...newConfig };
    this.initializeAuth();
  }
}

// Export singleton instance
export default GmailService.getInstance();