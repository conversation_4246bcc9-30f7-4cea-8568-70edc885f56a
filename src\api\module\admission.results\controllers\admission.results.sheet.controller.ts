import { google } from 'googleapis';
import * as fs from 'fs';
import { Request, Response, NextFunction } from 'express';
import <PERSON>Handler from '../../../middlewares/success';
import SheetsWriter from '../../../../config/sheets';

// Interface cho request lookup
interface LookupRequest {
  major: string;
  subjectCombination: string;
  scores: number[];
  academicRank?: string;
  name: string;
  phone: string;
  email: string;
}

// Interface cho kết quả lookup
interface LookupResult {
  major: string;
  subjectCombination: string;
  scores: number[];
  totalScore: number;
  academicRank: string | null;
  academicBonus: number;
  finalScore: number;
  thresholdScore: number;
  isAdmitted: boolean;
  message: string;
  details: {
    scoreDifference: number;
    availableCombinations: string[];
    academicRankRequired: string;
    academicRankValid: boolean;
  };
}

const CREDENTIALS_PATH = './src/credential/service-account.json';

const auth = new google.auth.GoogleAuth({
  credentials: JSON.parse(fs.readFileSync(CREDENTIALS_PATH, 'utf-8')),
  scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly'],
});

const sheets = google.sheets({ version: 'v4', auth });

const SPREADSHEET_ID = '1GMhXSOJ3cUKxxRWdnSE6P9l78nBpTevsTpq4gcNlTqo';
const SHEET_NAME = 's1';

class AdmissionResultController {
  public static async get(req: any, res: Response, next: NextFunction) {
    try {
      const keyword = req.params.cccd;
      const range = `${SHEET_NAME}!A1:Z`;
      const response = await sheets.spreadsheets.values.get({
        spreadsheetId: SPREADSHEET_ID,
        range: range,
      });
      let result = {};
      const rows = response.data.values || [];
      for (const row of rows) {
        if (row[5] && row[5].toString().trim() === keyword.trim()) {
          result = Object.assign(result, {
            name: `${row[2]} ${row[3]}`,
            major: row[6],
            conditional_admission: row[12] ? true : false,
          });
        }
      }
      SuccessHandler.success(result, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async lookup(req: any, res: Response, next: NextFunction) {
    try {
      // Lấy thông tin từ request body
      const { major, subjectCombination, scores, academicRank, name, phone, email }: LookupRequest = req.body;

      // Validate input
      if (!major || !subjectCombination || !scores || !Array.isArray(scores) || scores.length !== 3) {
        return res.status(400).json({
          success: false,
          message: 'Thiếu thông tin bắt buộc: ngành học, tổ hợp môn, và điểm 3 môn'
        });
      }

      // Validate thông tin cá nhân
      if (!name || !phone || !email) {
        return res.status(400).json({
          success: false,
          message: 'Thiếu thông tin cá nhân: họ tên, số điện thoại, và email'
        });
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({
          success: false,
          message: 'Email không hợp lệ'
        });
      }

      // Validate phone format (số điện thoại Việt Nam)
      const phoneRegex = /^(0|\+84)[0-9]{9,10}$/;
      if (!phoneRegex.test(phone)) {
        return res.status(400).json({
          success: false,
          message: 'Số điện thoại không hợp lệ'
        });
      }

      // Điểm chuẩn các ngành học theo tổ hợp môn
      const admissionThresholds: { [key: string]: { [key: string]: number } } = {
        'Y khoa': {
          A02: 22.5,
          B00: 22.5,
          B08: 22.5,
        },
        'Điều dưỡng': {
          A00: 19,
          A02: 19,
          B00: 19,
          B08: 19,
          D07: 19,
        },
        'Kỹ thuật Xét nghiệm Y học': {
          A00: 19,
          A02: 19,
          B00: 19,
          B08: 19,
          D07: 19,
        },
        'Quản trị kinh doanh': {
          A00: 15,
          A01: 15,
          C01: 15,
          D01: 15,
          D07: 15,
          X01: 15,
        },
        'Kinh doanh quốc tế': {
          A00: 15,
          A01: 15,
          C01: 15,
          D01: 15,
          D07: 15,
          X01: 15,
        },
        'Tài chính - Ngân hàng': {
          A00: 15,
          A01: 15,
          C01: 15,
          D01: 15,
          D07: 15,
          X01: 15,
        },
        'Kế toán': {
          A00: 15,
          A01: 15,
          C01: 15,
          D01: 15,
          D07: 15,
          X01: 15,
        },
        'Khoa học máy tính': {
          A00: 15,
          A01: 15,
          D01: 15,
          D07: 15,
          X07: 15,
          X25: 15,
          X26: 15,
          X27: 15,
          X56: 15,
        },
        'Công nghệ sinh học': {
          A02: 15,
          B00: 15,
          B03: 15,
          B08: 15,
          X14: 15,
          X16: 15,
        },
        'Ngôn ngữ Anh': {
          A01: 15,
          D01: 15,
          D07: 15,
          D08: 15,
          X25: 15,
        },
        'Trí tuệ nhân tạo': {
          A00: 15,
          A01: 15,
          D01: 15,
          D07: 15,
          X07: 15,
          X25: 15,
          X26: 15,
          X27: 15,
          X56: 15,
        },
        'Khoa học dữ liệu': {
          A00: 15,
          A01: 15,
          D01: 15,
          X07: 15,
          X25: 15,
          X26: 15,
          X27: 15,
          X56: 15,
        },
        'Digital Marketing': {
          A00: 15,
          A01: 15,
          C01: 15,
          D01: 15,
          D07: 15,
          X01: 15,
        },
      };

      // Kiểm tra ngành học có tồn tại không
      if (!admissionThresholds[major]) {
        return res.status(400).json({
          success: false,
          message: `Ngành học "${major}" không tồn tại trong hệ thống`
        });
      }

      // Kiểm tra tổ hợp môn có phù hợp với ngành học không
      const majorThresholds = admissionThresholds[major];
      if (!majorThresholds[subjectCombination]) {
        return res.status(400).json({
          success: false,
          message: `Tổ hợp môn "${subjectCombination}" không phù hợp với ngành "${major}"`
        });
      }

      // Tính tổng điểm 3 môn
      const totalScore = scores.reduce((sum: number, score: number) => {
        const numScore = parseFloat(score.toString());
        if (isNaN(numScore) || numScore < 0 || numScore > 10) {
          throw new Error('Điểm phải là số từ 0 đến 10');
        }
        return sum + numScore;
      }, 0);

      // Lấy điểm chuẩn của ngành và tổ hợp môn
      const thresholdScore = majorThresholds[subjectCombination];

      // Kiểm tra yêu cầu xếp loại học bạ cho các ngành đặc biệt
      let academicRankValid = true;
      let academicRankMessage = '';

      if (major === 'Y khoa') {
        // Y khoa yêu cầu xếp loại "tốt"
        if (!academicRank || academicRank.toLowerCase() !== 'tốt') {
          academicRankValid = false;
          academicRankMessage = 'Ngành Y khoa yêu cầu xếp loại học bạ "tốt"';
        }
      } else if (major === 'Điều dưỡng' || major === 'Kỹ thuật Xét nghiệm Y học') {
        // Điều dưỡng và Kỹ thuật Xét nghiệm Y học yêu cầu xếp loại "khá" hoặc "tốt"
        if (!academicRank || !['khá', 'tốt'].includes(academicRank.toLowerCase())) {
          academicRankValid = false;
          academicRankMessage = `Ngành ${major} yêu cầu xếp loại học bạ "khá" hoặc "tốt"`;
        }
      }

      // Tổng điểm cuối cùng (không cộng điểm ưu tiên)
      const finalScore = totalScore;

      // Kiểm tra kết quả trúng tuyển (phải đạt cả điểm số và xếp loại học bạ)
      const isAdmitted = finalScore >= thresholdScore && academicRankValid;

      // Chuẩn bị thông báo kết quả
      let message = '';
      if (isAdmitted) {
        message = `Chúc mừng! Bạn đã trúng tuyển ngành ${major} với ${finalScore} điểm (điểm chuẩn: ${thresholdScore})`;
      } else if (!academicRankValid) {
        message = `Rất tiếc! ${academicRankMessage}`;
      } else {
        message = `Rất tiếc! Bạn chưa đạt điểm chuẩn ngành ${major}. Điểm của bạn: ${finalScore}, điểm chuẩn: ${thresholdScore}`;
      }

      // Chuẩn bị kết quả trả về
      const result: LookupResult = {
        major,
        subjectCombination,
        scores,
        totalScore: parseFloat(totalScore.toFixed(2)),
        academicRank: academicRank || null,
        academicBonus: 0, // Không cộng điểm ưu tiên
        finalScore: parseFloat(finalScore.toFixed(2)),
        thresholdScore,
        isAdmitted,
        message,
        details: {
          scoreDifference: parseFloat((finalScore - thresholdScore).toFixed(2)),
          availableCombinations: Object.keys(majorThresholds),
          academicRankRequired: major === 'Y khoa' ? 'tốt' :
                               (major === 'Điều dưỡng' || major === 'Kỹ thuật Xét nghiệm Y học') ? 'khá hoặc tốt' : 'không yêu cầu',
          academicRankValid
        }
      };

      // Ghi thông tin tra cứu lên Google Sheets
      try {
        const sheetsWriter = SheetsWriter;

        // Lấy số thứ tự tiếp theo
        const currentData = await sheetsWriter.getSheets().spreadsheets.values.get({
          spreadsheetId: sheetsWriter.getConfig().spreadsheetId,
          range: `${sheetsWriter.getConfig().sheetName}!A:A`,
        });

        const nextNumber = (currentData.data.values?.length || 0) + 1;

        // Chuẩn bị dữ liệu để ghi
        const rowData = [
          nextNumber,           // Cột A: Số thứ tự
          name,                 // Cột B: Họ tên
          phone,                // Cột C: Số điện thoại
          email,                // Cột D: Email
          major,                // Cột E: Ngành học
          subjectCombination,   // Cột F: Tổ hợp môn
          finalScore,           // Cột G: Điểm cuối cùng
          isAdmitted ? 'Trúng tuyển' : 'Không trúng tuyển', // Cột H: Kết quả
          new Date() // Cột I: Thời gian tra cứu
        ];

        // Ghi dữ liệu lên sheet
        await sheetsWriter.appendData([rowData]);

        console.log(`✅ Đã ghi thông tin tra cứu của ${name} lên Google Sheets`);
      } catch (sheetError) {
        console.error('❌ Lỗi khi ghi lên Google Sheets:', sheetError);
        // Không throw error để không ảnh hưởng đến kết quả tra cứu
      }

      SuccessHandler.success(result, req, res);

    } catch (error) {
      if (error instanceof Error) {
        return res.status(400).json({
          success: false,
          message: error.message
        });
      }
      next(error);
    }
  }
}

export default AdmissionResultController;
