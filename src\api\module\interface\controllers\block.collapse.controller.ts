// 5/4/2024
import { Request, Response, NextFunction } from 'express';
import BlockCollapse from '../models/block.collapse.model';
import <PERSON><PERSON>r<PERSON><PERSON><PERSON> from '../../../middlewares/error';
import <PERSON><PERSON>andler from '../../../middlewares/success';

class BlockCollapseController {
  public static async load(req: any, res: Response, next: NextFunction, id: string) {
    try {
      const collapse = await BlockCollapse.get(id);
      req.locals = { collapse };
      return next();
    } catch (error) {
      return ErrorHandler.handler(error, req, res);
    }
  }

  public static async get(req: any, res: Response, next: NextFunction) {
    try {
      SuccessHandler.success(req.locals.collapse.transform(), req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async remove(req: any, res: Response, next: NextFunction) {
    try {
      const { collapse } = req.locals;
      const now = new Date();

      const deleted = Object.assign(collapse, {
        deleted_by: req.user._id,
        deleted_at: now,
        is_active: false,
      });
      await deleted.save();
      SuccessHandler.success(deleted, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async list(req: Request, res: Response, next: NextFunction) {
    try {
      const collapses = await BlockCollapse.list(req.query);
      const count = await BlockCollapse.countItem(req.query);
      const transformed = collapses.map((collapse: any) => collapse.transform());
      SuccessHandler.success({ total: count, docs: transformed }, req, res);
    } catch (error: any) {
      next(error);
    }
  }

  public static async update(req: any, res: Response, next: NextFunction) {
    try {
      const { collapse } = req.locals;
      const { body, user } = req;
      const now = new Date();
      body.updated_by = user._id;
      body.updated_at = now;

      const updated = Object.assign(collapse, body);
      await updated.save();
      SuccessHandler.success(updated, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async create(req: any, res: Response, next: NextFunction) {
    try {
      const { body, user } = req;
      const now = new Date();
      body.created_at_by = user._id;
      body.created_at = now;
      const collapse = new BlockCollapse(body);
      await collapse.save();
      SuccessHandler.success(collapse, req, res);
    } catch (error) {
      next(error);
    }
  }
}

export default BlockCollapseController;
