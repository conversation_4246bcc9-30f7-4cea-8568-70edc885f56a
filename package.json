{"name": "tuyen-sinh-typescript", "version": "0.0.1", "description": "TTU tuyển sinh typescript", "main": "src/index.ts", "repository": "https://github.com/dn1ne/tuyen-sinh-typescript.git", "author": "dn1ne <<EMAIL>>", "license": "MIT", "scripts": {"dev": "nodemon ./src/index.ts", "build": "tsc", "start": "pm2 start ./dist/index.js --name crm-api --watch"}, "dependencies": {"axios": "^1.8.4", "bcryptjs": "^2.4.3", "bluebird": "^3.7.2", "body-parser": "^1.20.0", "compression": "^1.7.4", "cors": "^2.8.5", "cron": "^2.0.0", "dotenv": "^16.0.0", "express": "^4.17.3", "express-validation": "^4.1.0", "googleapis": "^149.0.0", "helmet": "^5.0.2", "http-status": "^1.5.1", "image-size": "^1.0.1", "joi": "^17.6.0", "jwt-simple": "^0.5.6", "lodash": "^4.17.21", "method-override": "^3.0.0", "moment": "^2.29.3", "moment-timezone": "^0.5.34", "mongoose": "^6.3.0", "morgan": "^1.10.0", "multer": "^1.4.4", "mysql2": "^3.14.1", "nodemailer": "^6.7.5", "passport": "^0.5.2", "passport-http-bearer": "^1.0.1", "passport-jwt": "^4.0.0", "passport-local": "^1.0.0", "sequelize": "^6.37.7", "sharp": "^0.30.5", "slug": "^5.3.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/bluebird-global": "^3.5.13", "@types/compression": "^1.7.2", "@types/cors": "^2.8.12", "@types/cron": "^2.0.0", "@types/express": "^4.17.13", "@types/lodash": "^4.14.182", "@types/method-override": "^0.0.32", "@types/morgan": "^1.9.3", "@types/multer": "^1.4.7", "@types/node": "^17.0.31", "@types/nodemailer": "^6.4.4", "@types/passport-http-bearer": "^1.0.37", "@types/passport-jwt": "^3.0.6", "@types/passport-local": "^1.0.34", "@types/sharp": "^0.30.2", "@types/slug": "^5.0.3", "nodemon": "^2.0.15"}}