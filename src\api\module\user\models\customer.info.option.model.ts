// 19/4/2024
import { Schema, model, Document, Model, Types } from 'mongoose';
import { omitBy, isNil } from 'lodash';
import APIError from '../../../utils/APIError';
import httpStatus from 'http-status';

interface ICustomerInfoOption {
  key: string;
  value: string;
  note: string;
  name: string;

  is_active: boolean;
  created_by: Types.ObjectId;
  updated_by: Types.ObjectId;
  deleted_at: Date;
  deleted_by: Types.ObjectId;
}

interface ICustomerInfoOptionDocument extends ICustomerInfoOption, Document {
  transform(): {};
}

interface ICustomerInfoOptionModel extends Model<ICustomerInfoOptionDocument> {
  langOpts(): string[];
  get(id: string): any;
  all(): any;
  list({
    page,
    perPage,
    sort,
    key,
    is_active,
  }: {
    page?: number;
    perPage?: number;
    sort?: string;
    key?: string;
    is_active?: boolean;
  }): any;
  countItem({
    key, //
    is_active,
  }: {
    key?: string;
    is_active?: boolean;
  }): any;
}

const customerInfoOptionSchema = new Schema<ICustomerInfoOptionDocument>(
  {
    key: {
      type: String,
      required: true,
      trim: true,
    },
    value: {
      type: String,
      trim: true,
      required: true,
    },
    note: {
      type: String,
      trim: true,
    },
    name: {
      type: String,
      trim: true,
      required: true,
    },

    is_active: {
      type: Boolean,
      default: true,
    },
    created_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    updated_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    deleted_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    deleted_at: {
      type: Date,
    },
  },
  {
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  }
);

customerInfoOptionSchema.methods.transform = function (): {} {
  const transformed: { [index: string]: any } = {};
  const fields = [
    '_id', //
    'key',
    'value',
    'note',
    'name',

    'created_at',
    'is_active',
    'delete_at',
  ];

  fields.forEach((field: any) => {
    transformed[field] = this[field];
  });

  return transformed;
};

customerInfoOptionSchema.statics.list = function ({
  page = 1,
  perPage = 30,
  sort,
  key,
  is_active = true,
}: {
  page: number;
  perPage: number;
  sort: string;
  key: string;
  is_active: boolean;
}) {
  try {
    const options = omitBy(
      {
        key,
        is_active,
      },
      isNil
    );
    const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };
    const result = this.find(options).sort(sortOpts);
    if (perPage > -1) {
      result.skip(perPage * (page - 1)).limit(perPage);
    }
    return result.exec();
  } catch (error) {
    throw error;
  }
};

customerInfoOptionSchema.statics.all = function () {
  try {
    const res = this.aggregate([
      {
        $match: { is_active: true },
      },
      {
        $group: {
          _id: { key: '$key', name: '$name' },
          option: { $push: { _id: '$_id', value: '$value' } },
        },
      },
    ]);
    return res.exec();
  } catch (error) {
    throw error;
  }
};

customerInfoOptionSchema.statics.countItem = function ({
  key,
  is_active = true,
}: {
  key: string;
  is_active: boolean;
}) {
  try {
    const options = omitBy(
      {
        key,
        is_active,
      },
      isNil
    );
    return this.find(options).count().exec();
  } catch (error) {
    throw error;
  }
};

customerInfoOptionSchema.statics.get = async function (id: string) {
  try {
    const option = await this.findById(id).exec();
    if (option) {
      return option;
    }
    throw new APIError({
      message: 'Info Option does not exist',
      status: httpStatus.NOT_FOUND,
    });
  } catch (error) {
    throw error;
  }
};

const CustomerInfoOption = model<ICustomerInfoOptionDocument, ICustomerInfoOptionModel>(
  'CustomerInfoOption',
  customerInfoOptionSchema
);

export default CustomerInfoOption;
