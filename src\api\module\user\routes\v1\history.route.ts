// 4/3/2025
import { Router } from 'express';
import { validate } from 'express-validation';
import controller from '../../controllers/history.controller';
import validation from '../../validations/history.validation';
import Auth from '../../../../middlewares/auth';

const router = Router();
router.param('id', controller.load);

router
  .route('/') //
  .get(validate(validation.list), controller.list)
  .post(
    Auth.authorize([...Auth.USER, ...Auth.ADMIN]),
    validate(validation.create),
    controller.create
  );

router
  .route('/:id') //
  .get(controller.get)
  .patch(
    Auth.authorize([...Auth.USER, ...Auth.ADMIN]),
    validate(validation.patch),
    controller.update
  )
  .delete(
    Auth.authorize([...Auth.USER, ...Auth.ADMIN]),
    validate(validation.getOrDelete),
    controller.remove
  );

export default router;
