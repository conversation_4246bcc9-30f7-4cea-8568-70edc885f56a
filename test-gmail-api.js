// Test file để kiểm tra Gmail API
require('dotenv').config();

async function testGmailAPI() {
  console.log('📧 Testing Gmail API...\n');

  try {
    // Import GmailService (cần compile TypeScript trước)
    const { default: GmailService } = await import('./dist/src/config/gmail.js');
    
    console.log('✅ GmailService imported successfully');

    // Test 1: Kiểm tra kết nối
    console.log('\n🔗 Testing Connection...');
    const isConnected = await GmailService.testConnection();
    
    if (!isConnected) {
      console.log('❌ Gmail API connection failed');
      return;
    }

    // Test 2: Lấy profile
    console.log('\n👤 Getting Profile...');
    try {
      const profile = await GmailService.getProfile();
      console.log('✅ Profile retrieved:', JSON.stringify(profile, null, 2));
    } catch (error) {
      console.log('❌ Failed to get profile:', error.message);
    }

    // Test 3: <PERSON><PERSON>y labels
    console.log('\n🏷️ Getting Labels...');
    try {
      const labels = await GmailService.getLabels();
      console.log('✅ Labels retrieved:', labels.slice(0, 5).map(l => l.name)); // Hiển thị 5 labels đầu
    } catch (error) {
      console.log('❌ Failed to get labels:', error.message);
    }

    // Test 4: Gửi email đơn giản
    console.log('\n📤 Testing Simple Email...');
    try {
      const result = await GmailService.sendSimpleEmail(
        '<EMAIL>', // Thay bằng email test của bạn
        'Test Email from Gmail API',
        '<h1>Hello from Gmail API!</h1><p>This is a test email sent using Gmail API directly.</p><p>Time: ' + new Date().toISOString() + '</p>'
      );
      console.log('✅ Simple email sent:', result.id);
    } catch (error) {
      console.log('❌ Failed to send simple email:', error.message);
    }

    // Test 5: Gửi email với CC và BCC
    console.log('\n📤 Testing Email with CC/BCC...');
    try {
      const result = await GmailService.sendEmail({
        to: '<EMAIL>',
        cc: '<EMAIL>',
        bcc: '<EMAIL>',
        subject: 'Test Email with CC/BCC',
        html: '<h2>Test Email</h2><p>This email has CC and BCC recipients.</p>'
      });
      console.log('✅ Email with CC/BCC sent:', result.id);
    } catch (error) {
      console.log('❌ Failed to send email with CC/BCC:', error.message);
    }

    // Test 6: Gửi email hàng loạt
    console.log('\n📤 Testing Bulk Emails...');
    const bulkEmails = [
      {
        to: '<EMAIL>',
        subject: 'Bulk Email 1',
        html: '<p>This is bulk email #1</p>'
      },
      {
        to: '<EMAIL>',
        subject: 'Bulk Email 2',
        html: '<p>This is bulk email #2</p>'
      },
      {
        to: 'invalid-email', // Email không hợp lệ để test error handling
        subject: 'Bulk Email 3',
        html: '<p>This is bulk email #3</p>'
      }
    ];

    try {
      const results = await GmailService.sendBulkEmails(bulkEmails);
      console.log('✅ Bulk emails processed:', results);
    } catch (error) {
      console.log('❌ Failed to send bulk emails:', error.message);
    }

    // Test 7: Test apply notification (nếu có template)
    console.log('\n📧 Testing Apply Notification...');
    try {
      const applyData = {
        email: '<EMAIL>',
        name: 'Nguyễn Văn Test',
        phone: '0123456789',
        education_type: 'Đại học',
        major_interest_most: 'Công nghệ thông tin',
        now: new Date().toISOString()
      };

      const result = await GmailService.sendApplyNotification(applyData);
      console.log('✅ Apply notification sent:', result.id);
    } catch (error) {
      console.log('❌ Failed to send apply notification:', error.message);
    }

    // Test 8: Test advisor notification
    console.log('\n📧 Testing Advisor Notification...');
    try {
      const advisorData = {
        email: '<EMAIL>',
        name: 'Trần Thị Test',
        phone: '0987654321',
        advisor_name: 'Thầy Nguyễn Văn Advisor'
      };

      const result = await GmailService.sendAdvisorNotification(advisorData);
      console.log('✅ Advisor notification sent:', result.id);
    } catch (error) {
      console.log('❌ Failed to send advisor notification:', error.message);
    }

    console.log('\n🎉 All Gmail API tests completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

function testEnvironmentVariables() {
  console.log('\n🌍 Gmail Environment Variables Check:\n');

  const requiredVars = [
    'MAILER_CLIENT_ID',
    'MAILER_CLIENT_SECRET',
    'MAILER_REDIRECT_URI',
    'MAILER_REFRESH_TOKEN',
    'MAILER_EMAIL'
  ];

  const optionalVars = [
    'MAILER_APPLY_NOTIFICATION_ADDRESS'
  ];

  console.log('📋 Required Variables:');
  requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      console.log(`✅ ${varName}: ${varName.includes('SECRET') || varName.includes('TOKEN') ? '***' : value}`);
    } else {
      console.log(`❌ ${varName}: Not set`);
    }
  });

  console.log('\n📋 Optional Variables:');
  optionalVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      console.log(`✅ ${varName}: ${value}`);
    } else {
      console.log(`⚠️  ${varName}: Not set (using default)`);
    }
  });
}

// Chạy tất cả tests
async function runAllTests() {
  console.log('🔬 Gmail API Test Suite');
  console.log('=' .repeat(50));

  // Test environment variables trước
  testEnvironmentVariables();

  // Test Gmail API
  await testGmailAPI();

  console.log('\n💡 Notes:');
  console.log('- Make sure to replace test email addresses with real ones');
  console.log('- Check Gmail sent folder for sent emails');
  console.log('- Verify OAuth2 credentials are correct');
  console.log('- Enable Gmail API in Google Cloud Console');
}

// Chạy tests
runAllTests().catch(console.error);
