// 9/4/2024
import { Router } from 'express';
import { validate } from 'express-validation';
import controller from '../../controllers/block.collapse.controller';
import validation from '../../validations/block.collapse.validation';
import Auth from '../../../../middlewares/auth';

const router = Router();
router.param('id', controller.load);

router
  .route('/') //
  .get(validate(validation.list), controller.list)
  .post(Auth.authorize(Auth.ADMIN), validate(validation.create), controller.create);

router
  .route('/:id') //
  .get(controller.get)
  .patch(Auth.authorize(Auth.ADMIN), validate(validation.patch), controller.update)
  .delete(Auth.authorize(Auth.ADMIN), validate(validation.getOrDelete), controller.remove);

export default router;
