// Test script để kiểm tra chức năng filter history theo ngày tạo
const axios = require('axios');

// Cấu hình base URL
const BASE_URL = 'http://localhost:3000/api/v1/history'; // Adjust endpoint as needed

// Mock token (thay bằng token thật khi test)
const AUTH_TOKEN = 'your-admin-token-here';

const headers = {
  'Authorization': `Bearer ${AUTH_TOKEN}`,
  'Content-Type': 'application/json'
};

/**
 * Test lấy history theo khoảng thời gian
 */
async function testHistoryDateFilter() {
  console.log('\n📅 Test: History Date Filter');
  console.log('=' .repeat(50));

  // Test cases với các khoảng thời gian khác nhau
  const testCases = [
    {
      name: 'Hôm nay',
      created_from: new Date().toISOString().split('T')[0], // YYYY-MM-DD
      created_to: new Date().toISOString().split('T')[0]
    },
    {
      name: '7 ngày qua',
      created_from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      created_to: new Date().toISOString().split('T')[0]
    },
    {
      name: 'Tháng này',
      created_from: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
      created_to: new Date().toISOString().split('T')[0]
    },
    {
      name: 'Chỉ từ ngày (không có đến ngày)',
      created_from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      created_to: null
    },
    {
      name: 'Chỉ đến ngày (không có từ ngày)',
      created_from: null,
      created_to: new Date().toISOString().split('T')[0]
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n📝 Test: ${testCase.name}`);
    
    const params = {
      page: 1,
      perPage: 10
    };

    if (testCase.created_from) {
      params.created_from = testCase.created_from;
    }
    if (testCase.created_to) {
      params.created_to = testCase.created_to;
    }

    try {
      const response = await axios.get(BASE_URL, { headers, params });
      
      const data = response.data.data || response.data;
      console.log(`✅ Success! Found ${data.length} records`);
      console.log(`📅 Date range: ${testCase.created_from || 'N/A'} to ${testCase.created_to || 'N/A'}`);
      
      if (data.length > 0) {
        console.log(`📊 Sample records:`);
        data.slice(0, 3).forEach((record, index) => {
          console.log(`  ${index + 1}. Created: ${record.created_at}`);
          console.log(`     Content: ${record.content?.substring(0, 50) || 'No content'}...`);
        });
      }

    } catch (error) {
      console.log(`❌ Error: ${error.response?.data?.message || error.message}`);
    }
  }
}

/**
 * Test với MongoDB query trực tiếp
 */
function testDirectMongoQuery() {
  console.log('\n🗄️  MongoDB Query Examples');
  console.log('=' .repeat(50));

  console.log('📝 Query để lấy history trong khoảng thời gian:');
  console.log(`
// Lấy history từ ngày đến ngày
db.histories.find({
  created_at: {
    $gte: ISODate("2024-01-01T00:00:00.000Z"),
    $lte: ISODate("2024-01-31T23:59:59.999Z")
  }
}).sort({ created_at: -1 })

// Lấy history từ ngày cụ thể trở đi
db.histories.find({
  created_at: {
    $gte: ISODate("2024-01-15T00:00:00.000Z")
  }
}).sort({ created_at: -1 })

// Lấy history đến ngày cụ thể
db.histories.find({
  created_at: {
    $lte: ISODate("2024-01-31T23:59:59.999Z")
  }
}).sort({ created_at: -1 })
  `);

  console.log('📝 Aggregation với filter phức tạp:');
  console.log(`
// Thống kê history theo ngày trong khoảng thời gian
db.histories.aggregate([
  {
    $match: {
      created_at: {
        $gte: ISODate("2024-01-01T00:00:00.000Z"),
        $lte: ISODate("2024-01-31T23:59:59.999Z")
      }
    }
  },
  {
    $group: {
      _id: {
        year: { $year: "$created_at" },
        month: { $month: "$created_at" },
        day: { $dayOfMonth: "$created_at" }
      },
      count: { $sum: 1 },
      records: { $push: "$$ROOT" }
    }
  },
  {
    $sort: { "_id.year": -1, "_id.month": -1, "_id.day": -1 }
  }
])
  `);
}

/**
 * Test với các format ngày khác nhau
 */
async function testDateFormats() {
  console.log('\n📅 Test: Different Date Formats');
  console.log('=' .repeat(50));

  const dateFormats = [
    {
      name: 'ISO Date String',
      created_from: '2024-01-01',
      created_to: '2024-01-31'
    },
    {
      name: 'Full ISO DateTime',
      created_from: '2024-01-01T00:00:00.000Z',
      created_to: '2024-01-31T23:59:59.999Z'
    },
    {
      name: 'Date with time',
      created_from: '2024-01-01 00:00:00',
      created_to: '2024-01-31 23:59:59'
    }
  ];

  for (const format of dateFormats) {
    console.log(`\n📝 Testing: ${format.name}`);
    console.log(`   From: ${format.created_from}`);
    console.log(`   To: ${format.created_to}`);

    try {
      // Test parsing dates
      const fromDate = new Date(format.created_from);
      const toDate = new Date(format.created_to);
      
      console.log(`   ✅ Parsed From: ${fromDate.toISOString()}`);
      console.log(`   ✅ Parsed To: ${toDate.toISOString()}`);
      
      // Test if dates are valid
      if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {
        console.log(`   ❌ Invalid date format`);
      } else {
        console.log(`   ✅ Valid date format`);
      }

    } catch (error) {
      console.log(`   ❌ Error parsing dates: ${error.message}`);
    }
  }
}

/**
 * Test performance với large dataset
 */
async function testPerformance() {
  console.log('\n⚡ Test: Performance with Date Filters');
  console.log('=' .repeat(50));

  const testCases = [
    {
      name: 'Small range (1 day)',
      created_from: new Date().toISOString().split('T')[0],
      created_to: new Date().toISOString().split('T')[0]
    },
    {
      name: 'Medium range (1 month)',
      created_from: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
      created_to: new Date().toISOString().split('T')[0]
    },
    {
      name: 'Large range (1 year)',
      created_from: new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0],
      created_to: new Date().toISOString().split('T')[0]
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n📊 Testing: ${testCase.name}`);
    
    const startTime = Date.now();
    
    try {
      const response = await axios.get(BASE_URL, {
        headers,
        params: {
          created_from: testCase.created_from,
          created_to: testCase.created_to,
          perPage: 100
        }
      });
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      const data = response.data.data || response.data;
      console.log(`   ✅ Found ${data.length} records in ${duration}ms`);
      
    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      console.log(`   ❌ Error after ${duration}ms: ${error.message}`);
    }
  }
}

/**
 * Chạy tất cả tests
 */
async function runAllTests() {
  console.log('🚀 History Date Filter Test Suite');
  console.log('=' .repeat(60));

  // Test 1: Basic date filtering
  await testHistoryDateFilter();

  // Test 2: Different date formats
  await testDateFormats();

  // Test 3: Performance testing
  await testPerformance();

  // Test 4: MongoDB query examples
  testDirectMongoQuery();

  console.log('\n🎉 All tests completed!');
  
  console.log('\n💡 Usage Examples:');
  console.log('   GET /api/v1/history?created_from=2024-01-01&created_to=2024-01-31');
  console.log('   GET /api/v1/history?created_from=2024-01-01');
  console.log('   GET /api/v1/history?created_to=2024-01-31');
  console.log('   GET /api/v1/history?created_from=2024-01-01&created_to=2024-01-31&customer=user_id');

  console.log('\n📋 Date Format Support:');
  console.log('   - YYYY-MM-DD (recommended)');
  console.log('   - YYYY-MM-DDTHH:mm:ss.sssZ (ISO format)');
  console.log('   - YYYY-MM-DD HH:mm:ss');

  console.log('\n⚠️  Notes:');
  console.log('   - created_from sets time to 00:00:00.000');
  console.log('   - created_to sets time to 23:59:59.999');
  console.log('   - Both parameters are optional');
  console.log('   - Can be combined with other filters (customer, advisor, etc.)');
}

// Chạy tests
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testHistoryDateFilter,
  testDateFormats,
  testPerformance,
  runAllTests
};
