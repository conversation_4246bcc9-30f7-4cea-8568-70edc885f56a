import nodemailer from 'nodemailer';
import { google } from 'googleapis';

import Vars from './vars';
import ApplyNotificationTemplate from '../api/template/applyNotification.template';
import AdvisorNotificationTemplate from '../api/template/advisorNotification.template';
const mail = Vars.config().mail;

class Nodemailer {
  // Khởi tạo OAuth2Client với Client ID và Client Secret
  private readonly oAuth2Client = new google.auth.OAuth2(
    mail.clientId,
    mail.clientSecret,
    mail.redirectUri
  );

  constructor() {
    // Set Refresh Token vào OAuth2Client Credentials
    this.oAuth2Client.setCredentials({
      refresh_token: mail.refreshToken,
    });
  }

  private async createTransporter() {
    const accessToken = await this.oAuth2Client.getAccessToken();

    return nodemailer.createTransport({
      service: 'gmail',
      auth: {
        type: 'OAuth2',
        user: mail.senderEmail,
        clientId: mail.clientId,
        clientSecret: mail.clientSecret,
        refreshToken: mail.refreshToken,
        accessToken: accessToken.token ?? '',
      },
    });
  }

  public sendApplyNotification = async ({
    email,
    name,
    phone,
    education_type,
    major_interest_most,
    now,
  }: {
    email: string;
    name: string;
    phone: string;
    education_type: string;
    major_interest_most: string;
    now: string;
  }) => {
    try {
      const transporter = await this.createTransporter();
      const mailOptions = {
        from: `Tan Tao University <${mail.senderEmail}>`,
        to: JSON.parse(mail.applyNotificationAddress),
        subject: `CRM - Thông báo đăng ký xét tuyển mới #${name}`,
        html: ApplyNotificationTemplate.get({
          email,
          name,
          phone,
          education_type,
          now,
          major_interest_most,
        }),
      };

      const result = await transporter.sendMail(mailOptions);
      console.log('Email sent:', result.envelope);
      return result;
    } catch (error) {
      console.error('Error sending email:', error);
      throw error;
    }
  };

  public sendAdvisorNotification = async ({
    email,
    name,
    phone,
    advisor_name,
  }: {
    email: string;
    name: string;
    phone: string;
    advisor_name: string;
  }) => {
    try {
      const transporter = await this.createTransporter();
      const mailOptions = {
        from: `Tan Tao University <${mail.senderEmail}>`,
        to: email,
        subject: `CRM - Thông báo phân công chăm sóc sinh viên #${name}`,
        html: AdvisorNotificationTemplate.get({
          name,
          phone,
          advisor_name,
        }),
      };
      const result = await transporter.sendMail(mailOptions);
      console.log('Email sent:', result.envelope);
      return result;
    } catch (error) {
      console.error('Error sending email:', error);
      throw error;
    }
  };
}

export default new Nodemailer();
