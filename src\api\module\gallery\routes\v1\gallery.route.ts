import { Router } from 'express';
import { validate } from 'express-validation';
import controller from '../../controllers/gallery.controller';
import validation from '../../validations/gallery.validation';
import Auth from '../../../../middlewares/auth';

const router = Router();
router.param('id', controller.load);

router
  .route('/') //
  .get(validate(validation.list), controller.list)
  .post(controller.create);

router
  .route('/:id')
  .get(Auth.authorize(), validate(validation.getOrDelete), controller.get)
  .delete(Auth.authorize(), validate(validation.getOrDelete), controller.remove)
  .patch(Auth.authorize(Auth.ADMIN), validate(validation.update), controller.update);

export default router;
