import Gallery from '../models/gallery.model';
import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON><PERSON><PERSON> from '../../../middlewares/success';
import <PERSON>rrorHandler from '../../../middlewares/error';
import Vars from '../../../../config/vars';
import multer from 'multer';
import sharp from 'sharp';
import moment from 'moment-timezone';
import path from 'path';
import common from '../../../services/common';
import fs from 'fs';
import sizeOf from 'image-size';
import { round } from 'lodash';
import httpStatus from 'http-status';

const { uploadDir, staticUri } = Vars.config();

class GalleryController {
  private static uploadPath = path.join(process.cwd(), uploadDir.upload);
  private static tmpPath = path.join(process.cwd(), uploadDir.tmp);
  private static avatarPath = path.join(process.cwd(), uploadDir.avatar);
  private static postContentPath = path.join(process.cwd(), uploadDir.post_content);
  private static postPath = path.join(process.cwd(), uploadDir.post);

  private static resize(
    fileSource: string,
    fileTarget: string,
    width: number,
    height: number
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      sharp(fileSource)
        .resize(width, height)
        .toFile(fileTarget, (err, result) => {
          if (err) {
            reject(err);
          }
          resolve(result);
        });
    });
  }

  public static async load(req: any, res: Response, next: NextFunction, id: string) {
    try {
      const gallery = await Gallery.get(id);
      req.locals = { gallery };
      return next();
    } catch (error) {
      return ErrorHandler.handler(error, req, res);
    }
  }

  public static async create(req: any, res: Response, next: NextFunction) {
    try {
      const fileStorage = multer.diskStorage({
        destination: (rq: any, file, cb: any) => {
          if (!fs.existsSync(GalleryController.uploadPath)) {
            fs.mkdirSync(GalleryController.uploadPath);
            if (!fs.existsSync(GalleryController.tmpPath)) {
              fs.mkdirSync(GalleryController.tmpPath);
            }
          }
          cb(null, GalleryController.tmpPath);
        },
        filename: (rq: any, file, cb: any) => {
          const datetimestamp = Date.now();
          cb(
            null,
            `${file.fieldname}-${datetimestamp}.${
              file.originalname.split('.')[file.originalname.split('.').length - 1]
            }`
          );
        },
      });

      const fileFilter = (req: any, file: any, cb: any) => {
        if (
          file.mimetype === 'image/png' ||
          file.mimetype === 'image/.svg' ||
          file.mimetype === 'image/webp' ||
          file.mimetype === 'image/jpg' ||
          file.mimetype === 'image/jpeg'
        ) {
          cb(null, true);
        } else {
          cb(new Error('File format should be PNG,JPG,JPEG,webp,svg'), false); // if validation failed then generate error
        }
      };

      const upload = multer({
        storage: fileStorage,
        fileFilter,
      }).single('file');

      upload(req, res, async (err) => {
        if (err) {
          return res.json({
            status: httpStatus.BAD_REQUEST,
            message: err.message,
          });
        }
        const now = moment();
        const year = now.format('YYYY');
        const month = now.format('MM');
        const day = now.format('DD');

        const type = req.body.type;
        switch (type) {
          case 'avatar': {
            const ext = path.extname(req.file.filename);
            const filename = path.basename(req.file.filename, ext);
            if (!fs.existsSync(GalleryController.avatarPath)) {
              fs.mkdirSync(GalleryController.avatarPath);
            }
            await common.move(
              `${GalleryController.tmpPath}/${req.file.filename}`,
              `${GalleryController.avatarPath}/${req.file.filename}`
            );
            await GalleryController.resize(
              `${GalleryController.avatarPath}/${req.file.filename}`,
              `${GalleryController.avatarPath}/${filename}-${type}${ext}`,
              200,
              200
            );
            await GalleryController.resize(
              `${GalleryController.avatarPath}/${req.file.filename}`,
              `${GalleryController.avatarPath}/${filename}-preview${ext}`,
              180,
              180
            );
            req.query.name = req.file.filename;
            req.file.filename = `avatar/${req.file.filename}`;
            req.file.directory = 'avatar';
            break;
          }
          case 'post_content': {
            const resolution = sizeOf(`${GalleryController.tmpPath}/${req.file.filename}`);
            const height = resolution.height || 0;
            const width = resolution.width || 0;
            if (!fs.existsSync(GalleryController.postContentPath)) {
              fs.mkdirSync(GalleryController.postContentPath);
            }
            let dir = `${GalleryController.postContentPath}/${year}`;
            if (!fs.existsSync(dir)) {
              fs.mkdirSync(dir);
            }

            dir = `${dir}/${month}`;
            if (!fs.existsSync(dir)) {
              fs.mkdirSync(dir);
            }

            dir = `${dir}/${day}`;
            if (!fs.existsSync(dir)) {
              fs.mkdirSync(dir);
            }
            const ext = path.extname(req.file.filename);
            const filename = path.basename(req.file.filename, ext);
            await common.move(
              `${GalleryController.tmpPath}/${req.file.filename}`,
              `${dir}/${req.file.filename}`
            );

            await GalleryController.resize(
              `${dir}/${req.file.filename}`,
              `${dir}/${filename}-${type}${ext}`,
              1000,
              round(1000 / (width / height))
            );
            await GalleryController.resize(
              `${dir}/${req.file.filename}`,
              `${dir}/${filename}-preview${ext}`,
              280,
              210
            );
            req.query.name = req.file.filename;
            req.file.filename = `post_content/${year}/${month}/${day}/${req.file.filename}`;
            req.file.directory = `post_content/${year}/${month}/${day}`;
            break;
          }
          case 'post': {
            if (!fs.existsSync(GalleryController.postPath)) {
              fs.mkdirSync(GalleryController.postPath);
            }
            let dir = `${GalleryController.postPath}/${year}`;
            if (!fs.existsSync(dir)) {
              fs.mkdirSync(dir);
            }

            dir = `${dir}/${month}`;
            if (!fs.existsSync(dir)) {
              fs.mkdirSync(dir);
            }

            dir = `${dir}/${day}`;
            if (!fs.existsSync(dir)) {
              fs.mkdirSync(dir);
            }
            const ext = path.extname(req.file.filename);
            const filename = path.basename(req.file.filename, ext);
            await common.move(
              `${GalleryController.tmpPath}/${req.file.filename}`,
              `${dir}/${req.file.filename}`
            );

            await GalleryController.resize(
              `${dir}/${req.file.filename}`,
              `${dir}/${filename}-${type}${ext}`,
              680,
              408
            );

            await GalleryController.resize(
              `${dir}/${req.file.filename}`,
              `${dir}/${filename}-preview${ext}`,
              280,
              210
            );

            req.query.name = req.file.filename;
            req.file.filename = `post/${year}/${month}/${day}/${req.file.filename}`;
            req.file.directory = `post/${year}/${month}/${day}`;
            break;
          }
          case 'origin': {
            if (!fs.existsSync(GalleryController.postContentPath)) {
              fs.mkdirSync(GalleryController.postContentPath);
            }
            let dir = `${GalleryController.postContentPath}/${year}`;
            if (!fs.existsSync(dir)) {
              fs.mkdirSync(dir);
            }

            dir = `${dir}/${month}`;
            if (!fs.existsSync(dir)) {
              fs.mkdirSync(dir);
            }

            dir = `${dir}/${day}`;
            if (!fs.existsSync(dir)) {
              fs.mkdirSync(dir);
            }
            const ext = path.extname(req.file.filename);
            const filename = path.basename(req.file.filename, ext);
            await common.move(
              `${GalleryController.tmpPath}/${req.file.filename}`,
              `${dir}/${req.file.filename}`
            );

            await GalleryController.resize(
              `${dir}/${req.file.filename}`,
              `${dir}/${filename}-preview${ext}`,
              280,
              210
            );
            req.query.name = req.file.filename;
            req.file.filename = `post_content/${year}/${month}/${day}/${req.file.filename}`;
            req.file.directory = `post_content/${year}/${month}/${day}`;
            break;
          }

          default:
            break;
        }
        const gallery = new Gallery({
          dir: req.file.filename,
          type,
          name: req.body.name,
          description: req.body.description,
        });
        await gallery.save();
        const dirArr = gallery.dir.split('.');
        gallery.link = `${staticUri}/${dirArr[0]}-${gallery.type}.${dirArr[1]}`;
        if(type==='origin'){
          gallery.link = `${staticUri}/${dirArr[0]}.${dirArr[1]}`;
        }
        gallery.link_preview = `${staticUri}/${dirArr[0]}-preview.${dirArr[1]}`;
        SuccessHandler.success(gallery, req, res);
      });
    } catch (error: any) {
      next(error);
    }
  }

  public static async list(req: Request, res: Response, next: NextFunction) {
    try {
      const forms = await Gallery.list(req.query);
      const count = await Gallery.countItem(req.query);
      const transformed = forms.map((gallery: any) => {
        const dirArr = gallery.dir.split('.');
        gallery.link = `${staticUri}/${dirArr[0]}-${gallery.type}.${dirArr[1]}`;
        gallery.link_preview = `${staticUri}/${dirArr[0]}-preview.${dirArr[1]}`;
        return gallery.transform();
      });
      SuccessHandler.success({ total: count, docs: transformed }, req, res);
    } catch (error: any) {
      next(error);
    }
  }

  public static async get(req: any, res: Response, next: NextFunction) {
    try {
      SuccessHandler.success(req.locals.gallery, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async remove(req: any, res: Response, next: NextFunction) {
    try {
      const { gallery } = req.locals;
      const deleted = await gallery.remove();
      SuccessHandler.success(deleted, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async update(req: any, res: Response, next: NextFunction) {
    try {
      req.body.updated_by = req.user._id;
      const gallery = Object.assign(req.locals.gallery, req.body);
      const updated = await gallery.save();
      SuccessHandler.success(updated, req, res);
    } catch (error) {
      next(error);
    }
  }
}

export default GalleryController;
