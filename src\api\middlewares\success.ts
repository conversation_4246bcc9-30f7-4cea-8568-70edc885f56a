import httpStatus from 'http-status';
import { Request, Response, NextFunction } from 'express';

class SuccessHandler {
  /**
   * Success.
   * @public
   */
  public static success(obj: {}, req: Request, res: Response, next?: NextFunction) {
    const response = {
      code: httpStatus.OK,
      message: 'Success',
      data: obj,
    };
    res.status(httpStatus.OK);
    res.json(response);
    res.end();
  }
}

export default SuccessHandler;
