import { Request, Response, NextFunction } from 'express';
import moment from 'moment-timezone';
import User from '../models/user.model';
import APIError from '../../../utils/APIError';
import httpStatus from 'http-status';
import <PERSON>Handler from '../../../middlewares/success';
import Vars from '../../../../config/vars';
import crypto from 'crypto';
import { ExtractJwt } from 'passport-jwt';
import jwt from 'jwt-simple';
// import Nodemailer from '../../../../config/nodemailer';
import Slug from 'slug';

class AuthController {
  private static generateTokenResponse(user: any, accessToken: any): any {
    const tokenType = 'Bearer';
    // const refreshToken = RefreshToken.generate(user).token;
    const expiresIn = moment().add(Vars.config().jwtExpirationInterval, 'minutes');
    return {
      tokenType,
      accessToken,
      // refreshToken,
      expiresIn,
    };
  }

  public static async register(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const activeToken = crypto.randomBytes(16).toString('hex');
      let user_id = '';
      const checkToken = ExtractJwt.fromAuthHeaderAsBearerToken()(req);
      if (checkToken) {
        const decoded = jwt.decode(checkToken, Vars.config().jwtSecret);
        user_id = decoded.sub;
        const getUser = await User.get(user_id);
        if (getUser) {
          if (getUser.role !== 'admin') {
            if (req.body.role === 'admin') {
              throw new APIError({
                message: 'User does not create admin permissions ',
                status: httpStatus.NOT_FOUND,
              });
            }
          }
        }
      } else if (req.body.role === 'admin') {
        throw new APIError({
          message: 'User does not create admin permissions ',
          status: httpStatus.NOT_FOUND,
        });
      }

      req.body.active_token = activeToken;
      req.body.is_active = false;
      const userNameGenerate = async (name: string): Promise<any> => {
        const user_name = `${Slug(req.body.name, '.').toLowerCase()}.${Math.floor(
          10 + Math.random() * 90
        )}`;
        const checkExist = await User.count({ user_name });
        if (checkExist > 0) {
          return userNameGenerate(name);
        }
        if (req.body.phone) {
          const phone = await User.count({ phone: req.body.phone });
          if (phone > 0) {
            throw new APIError({
              message: 'Validation Error',
              errors: [
                {
                  field: 'phone',
                  location: 'body',
                  messages: ['phone already exists'],
                },
              ],
              status: httpStatus.CONFLICT,
            });
          }
        }
        if (req.body.email) {
          const email = await User.count({ email: req.body.email });
          if (email > 0) {
            throw new APIError({
              message: 'Validation Error',
              errors: [
                {
                  field: 'email',
                  location: 'body',
                  messages: ['email already exists'],
                },
              ],
              status: httpStatus.CONFLICT,
            });
          }
        }

        return user_name;
      };
      req.body.user_name = await userNameGenerate(req.body.name);
      const user = await new User(req.body).save();

      const userTransformed = user.transform();
      const token = AuthController.generateTokenResponse(user, user.token());
      res.status(httpStatus.CREATED);
      return SuccessHandler.success({ token, user: userTransformed }, req, res);
    } catch (error) {
      return next(User.checkDuplicateEmail(error));
    }
  }

  public static async login(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { user, accessToken } = await User.findAndGenerateToken(req.body);

      // if (!user.is_active) {
      if (!user) {
        throw new APIError({
          message: 'Account does not active',
          status: httpStatus.FORBIDDEN,
        });
      } else {
        const token = AuthController.generateTokenResponse(user, accessToken);

        const userTransformed = user.transform();
        return SuccessHandler.success({ token, userTransformed }, req, res);
      }
    } catch (error) {
      return next(error);
    }
  }

  public static async active(req: Request, res: Response, next: NextFunction): Promise<any> {
    try {
      const { id } = req.params;
      const { active_code } = req.body;
      const user = await User.getActivatingUser({ id, active_code });
      const activatedUser = Object.assign(user, { active_code: null, is_active: true });
      await activatedUser.save();
      SuccessHandler.success({ data: activatedUser.transform() }, req, res);
    } catch (error) {
      return next(error);
    }
  }

  public static async changePassword(req: any, res: Response, next: NextFunction): Promise<any> {
    try {
      req.body.email = req.user.email;
      const { user, accessToken } = await User.findAndGenerateToken(req.body);
      const token = AuthController.generateTokenResponse(user, accessToken);
      const userTransformed = user.transform();

      user.password = req.body.newpassword;
      await user.save();

      return SuccessHandler.success({ token, user: userTransformed }, req, res);
    } catch (error) {
      return next(error);
    }
  }

  public static async forgotPassword(req: any, res: Response, next: NextFunction): Promise<any> {
    try {
      const { email } = req.body;
      const user = await User.getOneBy({ email });
      const recoveryCode = Math.floor(100000 + Math.random() * 900000);
      const updated = Object.assign(user, { recovery_code: recoveryCode });
      await updated.save();
      // const sendMail = await Nodemailer.sendRecoveryCode({
      //   email: email || '<EMAIL>',
      //   code: recoveryCode,
      // });
      // console.log(sendMail);
      SuccessHandler.success({}, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async setNewPassword(req: any, res: Response, next: NextFunction): Promise<any> {
    try {
      const { email, password, recovery_code } = req.body;
      const user = await User.getOneBy({ email, recovery_code });
      const updated = Object.assign(user, { password, recovery_code: null });
      await updated.save();
      SuccessHandler.success({}, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async sendActiveCode(req: any, res: Response, next: NextFunction): Promise<any> {
    try {
      const { user } = req;
      let { email } = req.body;
      if (!user.email && !email) {
        throw new APIError({
          message: 'User does not have email ',
          status: httpStatus.NOT_FOUND,
        });
      }
      const activeCode = Math.floor(100000 + Math.random() * 900000);
      let updated = new User({});
      if (email) {
        updated = Object.assign(user, { email, active_code: activeCode });
      } else {
        updated = Object.assign(user, { active_code: activeCode });
        email = user.email;
      }
      // const sendMail = await Nodemailer.sendActiveCode({
      //   email: email,
      //   code: activeCode,
      // });
      // console.log(sendMail);
      // await updated.save();
      SuccessHandler.success({}, req, res);
    } catch (error) {
      next(error);
    }
  }
}

export default AuthController;
