// 2/5/2024
import { Request, Response, NextFunction } from 'express';
import CustomerInfo from '../models/customer.info.model';
import CustomerInfoOption from '../models/customer.info.option.model';
import User from '../models/user.model';
import ErrorHandler from '../../../middlewares/error';
import SuccessHandler from '../../../middlewares/success';
import Xlsx from '../../../../config/xlsx';
import multer from 'multer';
import fs from 'fs';
import path from 'path';
import Vars from '../../../../config/vars';
import APIError from '../../../utils/APIError';
import httpStatus from 'http-status';
import validation from '../validations/customer.info.option.validation';
import moment from 'moment-timezone';

const { uploadDir } = Vars.config();

/////////////////////////////////
import axios from 'axios';
const baseURL = 'https://dktuyensinh.ttu.edu.vn/api/';
const api = axios.create({
  baseURL,
  timeout: 5000,
});
const createUser = async (data: {}): Promise<any> => {
  const res = await api.post('/users', data);
  return res.data;
};
///////////////////////////////////

// Đọc ngày trong file excel
const excelDateToJSDate = (serial: any): Date | null => {
  if (serial && typeof serial === 'number' && serial > 0 && serial < 2958466) {
    const utc_days = Math.floor(serial - 25569);
    const utc_value = utc_days * 86400; // seconds in a day
    const date_info = new Date(utc_value * 1000);

    // Ignore time zone by using getUTC* methods
    const fractional_day = serial - Math.floor(serial) + 0.0000001;
    let total_seconds = Math.floor(86400 * fractional_day);

    const seconds = total_seconds % 60;
    total_seconds -= seconds;

    const hours = Math.floor(total_seconds / (60 * 60));
    const minutes = Math.floor(total_seconds / 60) % 60;

    const jsDate = new Date(
      Date.UTC(
        date_info.getUTCFullYear(),
        date_info.getUTCMonth(),
        date_info.getUTCDate(),
        hours,
        minutes,
        seconds
      )
    );

    // Check if the resulting date is valid
    if (isNaN(jsDate.getTime())) {
      return null;
    }

    return jsDate;
  } else {
    return null;
  }
};

class CustomerInfoController {
  public static async load(req: any, res: Response, next: NextFunction, id: string) {
    try {
      const info = await CustomerInfo.get(id);
      req.locals = { info };
      return next();
    } catch (error) {
      return ErrorHandler.handler(error, req, res);
    }
  }

  public static async get(req: any, res: Response, next: NextFunction) {
    try {
      SuccessHandler.success(req.locals.info.transform(), req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async remove(req: any, res: Response, next: NextFunction) {
    try {
      const { info } = req.locals;
      const now = new Date();

      const deleted = Object.assign(info, {
        deleted_by: req.user._id,
        deleted_at: now,
        is_active: false,
      });
      await deleted.save();
      SuccessHandler.success(deleted, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async list(req: Request, res: Response, next: NextFunction) {
    try {
      const infos = await CustomerInfo.list(req.query);
      const count = await CustomerInfo.countItem(req.query);
      const transformed = infos.map((info: any) => info.transform());
      SuccessHandler.success({ total: count, docs: transformed }, req, res);
    } catch (error: any) {
      next(error);
    }
  }

  public static async update(req: any, res: Response, next: NextFunction) {
    try {
      const { info } = req.locals;
      const { body, user } = req;
      const now = new Date();
      body.updated_by = user._id;
      body.updated_at = now;

      const updated = Object.assign(info, body);
      await updated.save();
      SuccessHandler.success(updated, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async create(req: any, res: Response, next: NextFunction) {
    try {
      const { body, user } = req;
      const now = new Date();
      body.created_at_by = user._id;
      body.created_at = now;
      const info = new CustomerInfo(body);
      await info.save();
      SuccessHandler.success(info, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async importFromExcel(req: any, res: Response, next: NextFunction) {
    const fileStorage = multer.diskStorage({
      destination: (rq: any, file, cb: any) => {
        if (!fs.existsSync(uploadDir.upload)) {
          fs.mkdirSync(uploadDir.upload);
          if (!fs.existsSync(uploadDir.tmp)) {
            fs.mkdirSync(uploadDir.tmp);
          }
        }
        cb(null, path.join(process.cwd(), uploadDir.upload));
      },
      filename: (rq: any, file, cb: any) => {
        const datetimestamp = Date.now();
        cb(
          null,
          `${file.fieldname}-${datetimestamp}.${
            file.originalname.split('.')[file.originalname.split('.').length - 1]
          }`
        );
      },
    });

    const isValidEmail = (email: string): boolean => {
      const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      return emailPattern.test(email);
    };

    const fileFilter = (req: any, file: any, cb: any) => {
      if (
        file.mimetype === 'application/vnd.ms-excel' ||
        file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ) {
        cb(null, true);
      } else {
        cb(new Error('File format should be Excel file'), false); // if validation failed then generate error
      }
    };

    const upload = multer({
      storage: fileStorage,
      fileFilter,
    }).single('file');

    upload(req, res, async (err) => {
      try {
        const { error, value } = validation.importFromExcel.validate(req.body);

        if (err) {
          throw new APIError({
            message: err.message,
            status: httpStatus.BAD_REQUEST,
          });
        }
        if (error) {
          // Trả về lỗi nếu dữ liệu không hợp lệ
          throw new APIError({
            message: error.details[0].message,
            status: httpStatus.BAD_REQUEST,
          });
        }

        const cellular_network_arr = await CustomerInfoOption.list({
          perPage: -1,
          key: 'cellular_network',
        });

        const channel_arr = await CustomerInfoOption.list({
          perPage: -1,
          key: 'channel',
        });

        const school_arr = await CustomerInfoOption.list({
          perPage: -1,
          key: 'school',
        });

        const province_arr = await CustomerInfoOption.list({
          perPage: -1,
          key: 'province',
        });

        const district_arr = await CustomerInfoOption.list({
          perPage: -1,
          key: 'district',
        });

        const major_interest_most_arr = await CustomerInfoOption.list({
          perPage: -1,
          key: 'major_interest_most',
        });

        const city_interest_arr = await CustomerInfoOption.list({
          perPage: -1,
          key: 'city_interest',
        });

        const level_interest_arr = await CustomerInfoOption.list({
          perPage: -1,
          key: 'level_interest',
        });

        const data_source_arr = await CustomerInfoOption.list({
          perPage: -1,
          key: 'data_source',
        });

        const know_channel_arr = await CustomerInfoOption.list({
          perPage: -1,
          key: 'know_channel',
        });

        const is_apply_ttu_arr = await CustomerInfoOption.list({
          perPage: -1,
          key: 'is_apply_ttu',
        });

        const is_open_house_arr = await CustomerInfoOption.list({
          perPage: -1,
          key: 'is_open_house',
        });

        const is_uni_prep_arr = await CustomerInfoOption.list({
          perPage: -1,
          key: 'is_uni_prep',
        });

        const fee_reduction_type_arr = await CustomerInfoOption.list({
          perPage: -1,
          key: 'fee_reduction_type',
        });

        const data_status_arr = await CustomerInfoOption.list({
          perPage: -1,
          key: 'data_status',
        });

        const ttu_target_arr = await CustomerInfoOption.list({
          perPage: -1,
          key: 'ttu_target',
        });

        const moet_target_arr = await CustomerInfoOption.list({
          perPage: -1,
          key: 'moet_target',
        });

        const why_ttu_arr = await CustomerInfoOption.list({
          perPage: -1,
          key: 'why_ttu',
        });

        const why_not_ttu_arr = await CustomerInfoOption.list({
          perPage: -1,
          key: 'why_not_ttu',
        });

        const xlData = Xlsx.readData(req.file.path);
        for (let i = 0; i < xlData.length; i += 1) {
          const item: any = xlData[i];
          if (item['Số điện thoại']) {
            //kiểm tra trùng email hoặc phone
            let checkExist = 0;
            const phone = item['Số điện thoại'].toString();
            if (item['Email']) {
              const email = item['Email'];
              checkExist = await User.count({ $or: [{ phone }, { email }] });
            } else {
              checkExist = await User.count({ phone });
            }
            if (checkExist > 0) {
              continue;
            }

            const password = User.generatePassword();
            let cellular_network = null;
            let channel = null;
            let school = null;
            let province = null;
            let district = null;
            let major_interest_most = null;
            let city_interest = null;
            let level_interest = null;
            let data_source = null;
            let know_channel = null;
            let is_apply_ttu = null;
            let is_open_house = null;
            let is_uni_prep = null;
            let fee_reduction_type = null;
            let data_status = null;
            let ttu_target = null;
            let moet_target = null;
            let why_ttu = null;
            let why_not_ttu = null;

            let dob = null;
            const data_created_at = excelDateToJSDate(item['Ngày tiếp cận Data']);
            const data_updated_at = excelDateToJSDate(item['Ngày cập nhật tình trạng Data']);
            const next_care_at = excelDateToJSDate(item['Ngày chăm sóc tiếp theo']);
            const _class = item['Lớp'] || null;
            const email = item['Email'] || null;
            const major_interest = item['Ngành quan tâm khác'] || null;
            const university_interest = item['Trường Đại học đang quan tâm'] || null;
            const introducer = item['Thông tin người giới thiệu'] || null;
            const is_know_ttu = item['Đã biết đến TTU'] === 'Có';
            const fee_reduction_policy = item['Chính sách giảm học phí'] || null;
            const admission_scholarship = item['Học bổng Tuyển sinh'] || null;
            const talent_scholarship = item['Học bổng tài năng toàn phần'] === 'Có';
            const note = item['Ghi chú'] || null;

            for (let i = 0; i < cellular_network_arr.length; i += 1) {
              const element = cellular_network_arr[i];
              if (element.note.includes(phone.slice(0, 3))) {
                cellular_network = element._id;
              }
            }
            for (let i = 0; i < channel_arr.length; i += 1) {
              const element = channel_arr[i];
              if (element.value === item['Kênh Chăm sóc Data']) {
                channel = element._id;
              }
            }
            for (let i = 0; i < school_arr.length; i += 1) {
              const element = school_arr[i];
              if (element.value === item['Trường THPT']) {
                school = element._id;
              }
            }
            for (let i = 0; i < province_arr.length; i += 1) {
              const element = province_arr[i];
              if (element.value === item['Tỉnh/Thành phố']) {
                province = element._id;
              }
            }
            for (let i = 0; i < district_arr.length; i += 1) {
              const element = district_arr[i];
              if (element.value === item['Thành phố/Thị Xã/Huyện']) {
                district = element._id;
              }
            }
            for (let i = 0; i < major_interest_most_arr.length; i += 1) {
              const element = major_interest_most_arr[i];
              if (element.value === item['Ngành học quan tâm nhất']) {
                major_interest_most = element._id;
              }
            }
            for (let i = 0; i < city_interest_arr.length; i += 1) {
              const element = city_interest_arr[i];
              if (element.value === item['Chọn học Đại học tại Tỉnh/Thành phố']) {
                city_interest = element._id;
              }
            }
            for (let i = 0; i < level_interest_arr.length; i += 1) {
              const element = level_interest_arr[i];
              if (element.value === item['Lựa chọn cấp học sau tốt nghiệp THPT']) {
                level_interest = element._id;
              }
            }
            for (let i = 0; i < data_source_arr.length; i += 1) {
              const element = data_source_arr[i];
              if (element.value === item['Nguồn thu Data']) {
                data_source = element._id;
              }
            }
            for (let i = 0; i < know_channel_arr.length; i += 1) {
              const element = know_channel_arr[i];
              if (element.value === item['Đã biết đến TTU qua Kênh']) {
                know_channel = element._id;
              }
            }
            for (let i = 0; i < is_apply_ttu_arr.length; i += 1) {
              const element = is_apply_ttu_arr[i];
              if (element.value === item['Bạn quan tâm và Nộp HS vào TTU']) {
                is_apply_ttu = element._id;
              }
            }
            for (let i = 0; i < is_open_house_arr.length; i += 1) {
              const element = is_open_house_arr[i];
              if (element.value === item['Tham gia Open House']) {
                is_open_house = element._id;
              }
            }
            for (let i = 0; i < is_uni_prep_arr.length; i += 1) {
              const element = is_uni_prep_arr[i];
              if (element.value === item['Tham gia UniPrep']) {
                is_uni_prep = element._id;
              }
            }
            for (let i = 0; i < fee_reduction_type_arr.length; i += 1) {
              const element = fee_reduction_type_arr[i];
              if (element.value === item['Đối tượng']) {
                fee_reduction_type = element._id;
              }
            }
            for (let i = 0; i < data_status_arr.length; i += 1) {
              const element = data_status_arr[i];
              if (element.value === item['Kết quả tình trạng Data']) {
                data_status = element._id;
              }
            }
            for (let i = 0; i < ttu_target_arr.length; i += 1) {
              const element = ttu_target_arr[i];
              if (element.value === item['Ưu tiên TTU']) {
                ttu_target = element._id;
              }
            }
            for (let i = 0; i < moet_target_arr.length; i += 1) {
              const element = moet_target_arr[i];
              if (element.value === item['Nguyện vọng MOET']) {
                moet_target = element._id;
              }
            }
            for (let i = 0; i < why_ttu_arr.length; i += 1) {
              const element = why_ttu_arr[i];
              if (element.value === item['Lý do chọn học tại TTU']) {
                why_ttu = element._id;
              }
            }
            for (let i = 0; i < why_not_ttu_arr.length; i += 1) {
              const element = why_not_ttu_arr[i];
              if (element.value === item['Lý do không chọn học TTU']) {
                why_not_ttu = element._id;
              }
            }

            const withParent = is_open_house_arr.filter(
              (item: any) => item.value === 'Tham gia cùng phụ huynh'
            );

            if (item['Ngày sinh'] && item['Ngày sinh'] > 9999) {
              dob = excelDateToJSDate(item['Ngày sinh']);
            }
            if (item['Tham gia OH cùng phụ huynh'] === 'Có') {
              is_open_house = withParent[0]._id;
            }

            const newCustomer = new User({
              name: item['Họ Và Tên'] || '',
              phone: item['Số điện thoại'],
              role: 'customer',
              email: isValidEmail(email) ? email : null,
              dob,
              password,
              password_default: password,
            });
            const newInfo = new CustomerInfo({
              user: newCustomer._id,
              cellular_network,
              identify: item['Số CCCD'],
              data_created_at,
              data_updated_at,
              next_care_at,
              channel,
              school,
              class: _class,
              province,
              district,
              major_interest_most,
              major_interest,
              city_interest,
              level_interest,
              university_interest,
              data_source,
              introducer,
              is_know_ttu,
              know_channel,
              is_apply_ttu,
              is_open_house,
              is_uni_prep,
              fee_reduction_type,
              fee_reduction_policy,
              admission_scholarship,
              talent_scholarship,
              data_status,
              ttu_target,
              moet_target,
              why_ttu,
              why_not_ttu,
              note,
            });
            await newCustomer.save();
            await newInfo.save();
            console.log(`saved: ${i + 1}. ${newCustomer.name}`);
          }
        }

        SuccessHandler.success({}, req, res);
      } catch (error) {
        next(error);
      }
    });
  }

  public static async mappingUser(req: any, res: Response, next: NextFunction) {
    try {
      const infos = await CustomerInfo.list({ perPage: -1 });
      for (let i = 0; i < infos.length; i += 1) {
        const item = infos[i];
        if (item.user) {
          // await User.findOneAndUpdate({ _id: item.user }, { info: item._id.toString() });
          const user = await User.findByIdAndUpdate(item.user, { info: item._id.toString() });
          console.log('::', user?.name);
        }
      }
      SuccessHandler.success({}, req, res);
    } catch (error) {}
  }

  public static async createDocument(req: any, res: Response, next: NextFunction) {
    try {
      const { body } = req;
      const info = await CustomerInfo.get(body._id);
      if (info) {
        const user = await User.findById(info.user);
        if (user) {
          const data = {
            hoten: user.name,
            dienthoai: user.phone,
            email: user.email,
            gioitinh: user.gender === 'male' ? 1 : 2,
            ngaysinh: moment(user.dob).format('YYYY-MM-DD'),
            hedaotao: info.education_type,
            cmnd: info.identify,
          };
          const document = await createUser(data);
          await CustomerInfo.findOneAndUpdate({ _id: info._id }, { is_create_document: true });
          SuccessHandler.success(document, req, res);
        }
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        // 👉 Lỗi đến từ axios (ví dụ như lỗi 400, 500)
        console.error('❌ Lỗi từ server:', error.response?.data);

        // Nếu muốn truy cập status code hoặc message
        const status = error.response?.status;
        const message = error.response?.data?.message || 'Đã có lỗi xảy ra';

        console.log(`🧾 Status: ${status}, Message: ${message}`);
        error.message = error.response?.data?.message || 'Đã có lỗi xảy ra';
        next(error);
      } else {
        // 👉 Lỗi không phải từ axios (có thể lỗi cú pháp, network v.v.)
        console.error('❗ Lỗi không xác định:', error);
        next(error);
      }
    }
  }

  public static async syncData(req: any, res: Response, next: NextFunction) {
    try {
      const infos = await CustomerInfo.find({ education_type: { $ne: null } }).lean();
      for (let i = 0; i < infos.length; i += 1) {
        const item = { ...infos[i] };
        await CustomerInfo.findByIdAndUpdate(item._id, {
          data_created_at: item.created_at,
          data_source: '685268a1ad22c12a0c243ec6',
        });
      }
      SuccessHandler.success({}, req, res);
    } catch (error) {
      next(error);
    }
  }
}

export default CustomerInfoController;
