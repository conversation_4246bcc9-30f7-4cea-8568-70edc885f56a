// Test script để kiểm tra logic đã được cải thiện của userSchema.statics.list và countItem
const axios = require('axios');

// Cấu hình base URL
const BASE_URL = 'http://localhost:3000/api/v1/users';

// Mock token
const AUTH_TOKEN = 'your-admin-token-here';

const headers = {
  'Authorization': `Bearer ${AUTH_TOKEN}`,
  'Content-Type': 'application/json'
};

/**
 * Test improved logic consistency
 */
async function testImprovedLogicConsistency() {
  console.log('\n🔧 Test: Improved Logic Consistency');
  console.log('=' .repeat(70));

  const testCases = [
    {
      name: 'User fields only (no aggregation)',
      params: { 
        name: 'test',
        role: 'customer',
        is_active: true 
      },
      expectedBehavior: 'Should use find + populate'
    },
    {
      name: 'Info fields only (uses aggregation)',
      params: { 
        education_type: 'university'
      },
      expectedBehavior: 'Should use optimized aggregation pipeline'
    },
    {
      name: 'Boolean true handling',
      params: { 
        is_create_document: 'true'
      },
      expectedBehavior: 'Should filter for is_create_document = true'
    },
    {
      name: '<PERSON><PERSON>an false handling',
      params: { 
        is_create_document: 'false'
      },
      expectedBehavior: 'Should filter for is_create_document = false'
    },
    {
      name: 'Mixed user and info fields',
      params: { 
        name: 'nguyen',
        role: 'customer',
        education_type: 'university'
      },
      expectedBehavior: 'Should use aggregation with user filter first'
    },
    {
      name: 'Complex combination',
      params: { 
        name: 'test',
        role: 'customer',
        education_type: 'university',
        advisor: '507f1f77bcf86cd799439011',
        is_create_document: 'true'
      },
      expectedBehavior: 'Should use optimized aggregation pipeline'
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n📝 Testing: ${testCase.name}`);
    console.log(`   Params: ${JSON.stringify(testCase.params)}`);
    console.log(`   Expected: ${testCase.expectedBehavior}`);

    try {
      const startTime = Date.now();

      // Test list function
      const listResponse = await axios.get(BASE_URL, {
        headers,
        params: { ...testCase.params, page: 1, perPage: 10 }
      });

      // Test count function
      const countResponse = await axios.get(`${BASE_URL}/count`, {
        headers,
        params: testCase.params
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      const listData = listResponse.data.data || [];
      const totalCount = countResponse.data.data || countResponse.data;

      console.log(`   📊 Results: List=${listData.length}, Count=${totalCount} (${duration}ms)`);

      // Validate consistency
      if (typeof totalCount === 'number' && totalCount >= 0) {
        console.log(`   ✅ Count is valid number`);
      } else {
        console.log(`   ❌ Count is invalid: ${totalCount}`);
      }

      if (totalCount >= listData.length) {
        console.log(`   ✅ Consistency: Count >= List length`);
      } else {
        console.log(`   ❌ Inconsistency: Count (${totalCount}) < List length (${listData.length})`);
      }

      // Check data structure
      if (listData.length > 0) {
        const firstItem = listData[0];
        const hasRequiredFields = firstItem._id && firstItem.name !== undefined;
        const hasInfoStructure = firstItem.info !== undefined;
        
        console.log(`   📋 Structure: Required fields=${hasRequiredFields}, Info=${hasInfoStructure}`);
      }

    } catch (error) {
      console.log(`   ❌ Error: ${error.response?.data?.message || error.message}`);
    }
  }
}

/**
 * Test performance improvements
 */
async function testPerformanceImprovements() {
  console.log('\n⚡ Test: Performance Improvements');
  console.log('=' .repeat(70));

  const performanceTests = [
    {
      name: 'Simple query (find + populate)',
      params: { role: 'customer' },
      expectedApproach: 'find + populate'
    },
    {
      name: 'Info filter (optimized aggregation)',
      params: { education_type: 'university' },
      expectedApproach: 'aggregation with user filter first'
    },
    {
      name: 'Complex query (optimized aggregation)',
      params: { 
        name: 'test',
        role: 'customer',
        education_type: 'university',
        is_create_document: 'true'
      },
      expectedApproach: 'optimized aggregation pipeline'
    }
  ];

  for (const test of performanceTests) {
    console.log(`\n📊 Testing: ${test.name}`);
    console.log(`   Expected approach: ${test.expectedApproach}`);

    // Run multiple times to get average
    const times = [];
    const iterations = 3;

    for (let i = 0; i < iterations; i++) {
      const startTime = Date.now();
      
      try {
        await Promise.all([
          axios.get(BASE_URL, {
            headers,
            params: { ...test.params, page: 1, perPage: 20 }
          }),
          axios.get(`${BASE_URL}/count`, {
            headers,
            params: test.params
          })
        ]);
        
        const endTime = Date.now();
        times.push(endTime - startTime);
        
      } catch (error) {
        console.log(`   ❌ Error in iteration ${i + 1}: ${error.message}`);
      }
    }

    if (times.length > 0) {
      const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
      const minTime = Math.min(...times);
      const maxTime = Math.max(...times);

      console.log(`   ⏱️  Performance: Avg=${avgTime.toFixed(1)}ms, Min=${minTime}ms, Max=${maxTime}ms`);

      if (avgTime < 300) {
        console.log(`   ✅ Excellent performance`);
      } else if (avgTime < 500) {
        console.log(`   ✅ Good performance`);
      } else if (avgTime < 1000) {
        console.log(`   ⚠️  Acceptable performance`);
      } else {
        console.log(`   ❌ Slow performance - needs optimization`);
      }
    }
  }
}

/**
 * Test aggregation pipeline optimization
 */
async function testAggregationOptimization() {
  console.log('\n🔄 Test: Aggregation Pipeline Optimization');
  console.log('=' .repeat(70));

  console.log('📝 Old pipeline (inefficient):');
  console.log(`
// ❌ BEFORE: Match after lookup and unwind
{ $lookup: { from: 'customerinfos', ... } },
{ $unwind: '$info' },
{ $match: { name: /test/i, 'info.education_type': 'university' } }
// Problem: Processes all users, then filters
  `);

  console.log('📝 New pipeline (optimized):');
  console.log(`
// ✅ AFTER: Filter users first, then lookup
{ $match: { name: /test/i, role: 'customer' } },        // Filter users first
{ $lookup: { from: 'customerinfos', ... } },            // Join only filtered users
{ $unwind: '$info_detail' },                            // Unwind smaller dataset
{ $match: { 'info_detail.education_type': 'university' } } // Filter info
// Benefit: Processes fewer documents at each stage
  `);

  // Test with a query that should benefit from optimization
  const testParams = {
    name: 'test',
    role: 'customer',
    education_type: 'university'
  };

  console.log('\n📊 Testing optimized pipeline performance...');

  try {
    const startTime = Date.now();

    const [listResponse, countResponse] = await Promise.all([
      axios.get(BASE_URL, {
        headers,
        params: { ...testParams, page: 1, perPage: 50 }
      }),
      axios.get(`${BASE_URL}/count`, {
        headers,
        params: testParams
      })
    ]);

    const endTime = Date.now();
    const duration = endTime - startTime;

    const listData = listResponse.data.data || [];
    const totalCount = countResponse.data.data || countResponse.data;

    console.log(`   📊 Results: List=${listData.length}, Count=${totalCount}`);
    console.log(`   ⏱️  Total time: ${duration}ms`);

    // Validate that optimization works
    if (totalCount === listData.length || (listData.length < 50 && totalCount >= listData.length)) {
      console.log(`   ✅ Pipeline optimization working correctly`);
    } else {
      console.log(`   ⚠️  Potential pipeline issue detected`);
    }

  } catch (error) {
    console.log(`   ❌ Error: ${error.response?.data?.message || error.message}`);
  }
}

/**
 * Test data structure consistency
 */
async function testDataStructureConsistency() {
  console.log('\n📋 Test: Data Structure Consistency');
  console.log('=' .repeat(70));

  const structureTests = [
    {
      name: 'Simple query (populate)',
      params: { role: 'customer' }
    },
    {
      name: 'Aggregation query',
      params: { education_type: 'university' }
    }
  ];

  for (const test of structureTests) {
    console.log(`\n📝 Testing: ${test.name}`);

    try {
      const response = await axios.get(BASE_URL, {
        headers,
        params: { ...test.params, page: 1, perPage: 5 }
      });

      const data = response.data.data || [];

      if (data.length > 0) {
        const firstItem = data[0];
        
        console.log(`   📋 Sample structure:`);
        console.log(`      _id: ${firstItem._id ? 'present' : 'missing'}`);
        console.log(`      name: ${firstItem.name ? 'present' : 'missing'}`);
        console.log(`      email: ${firstItem.email !== undefined ? 'present' : 'missing'}`);
        console.log(`      role: ${firstItem.role ? 'present' : 'missing'}`);
        console.log(`      info: ${firstItem.info ? 'present' : 'missing'}`);
        
        if (firstItem.info) {
          console.log(`      info._id: ${firstItem.info._id ? 'present' : 'missing'}`);
          console.log(`      info.education_type: ${firstItem.info.education_type !== undefined ? 'present' : 'missing'}`);
          console.log(`      info.advisor: ${firstItem.info.advisor !== undefined ? 'present' : 'missing'}`);
          console.log(`      info.is_create_document: ${firstItem.info.is_create_document !== undefined ? 'present' : 'missing'}`);
        }

        console.log(`   ✅ Structure looks consistent`);
      } else {
        console.log(`   ℹ️  No data returned for this query`);
      }

    } catch (error) {
      console.log(`   ❌ Error: ${error.response?.data?.message || error.message}`);
    }
  }
}

/**
 * Chạy tất cả tests
 */
async function runAllTests() {
  console.log('🚀 Improved User Logic Test Suite');
  console.log('=' .repeat(80));

  // Test 1: Improved logic consistency
  await testImprovedLogicConsistency();

  // Test 2: Performance improvements
  await testPerformanceImprovements();

  // Test 3: Aggregation optimization
  await testAggregationOptimization();

  // Test 4: Data structure consistency
  await testDataStructureConsistency();

  console.log('\n🎉 All improved logic tests completed!');
  
  console.log('\n📋 Key Improvements Made:');
  console.log('   ✅ Separated user fields and info fields');
  console.log('   ✅ Optimized aggregation pipeline (filter users first)');
  console.log('   ✅ Consistent logic between list and countItem');
  console.log('   ✅ Better performance for complex queries');
  console.log('   ✅ Maintained data structure consistency');

  console.log('\n💡 Benefits:');
  console.log('   - Faster queries (filter users before lookup)');
  console.log('   - Consistent results between list and count');
  console.log('   - Better maintainability');
  console.log('   - Cleaner code structure');
  console.log('   - Same data structure regardless of query type');
}

// Chạy tests
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testImprovedLogicConsistency,
  testPerformanceImprovements,
  testAggregationOptimization,
  testDataStructureConsistency,
  runAllTests
};
