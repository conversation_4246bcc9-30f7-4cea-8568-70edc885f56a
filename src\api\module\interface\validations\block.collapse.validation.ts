// 5/4/2024
import <PERSON><PERSON> from 'joi';

const blockCollapseValidation = {
  list: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      is_active: Joi.boolean(),
      title: Joi.string(),
      content: Joi.string(),
    }),
  },

  create: {
    body: Joi.object({
      order: Joi.number(),
      title: Joi.string().required(),
      content: Joi.string().allow('').optional(),
      children: Joi.array().items(Joi.string().regex(/^[a-fA-F0-9]{24}$/)),
      parent: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
    }),
  },

  getOrDelete: {
    params: Joi.object({
      id: Joi.string()
        .regex(/^[a-fA-F0-9]{24}$/)
        .required(),
    }),
  },

  patch: {
    params: Joi.object({
      id: Joi.string()
        .regex(/^[a-fA-F0-9]{24}$/)
        .required(),
    }),
    body: Joi.object({
      order: Joi.number(),
      title: Joi.string(),
      content: Joi.string(),
      children: Joi.array().items(Joi.string().regex(/^[a-fA-F0-9]{24}$/)),
      parent: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
    }),
  },
};

export default blockCollapseValidation;
