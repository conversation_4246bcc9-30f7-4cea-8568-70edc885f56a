import Joi from 'joi';
import Page from '../models/page.model';

const pageValidation = {
  list: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      language: Joi.string().valid(...Page.langOpts()),
      is_active: Joi.boolean(),
      title: Joi.string(),
    }),
  },

  create: {
    body: Joi.object({
      language: Joi.string().valid(...Page.langOpts()),
      title: Joi.string().required(),
      slug: Joi.string(),
      cover: Joi.string().allow('').optional(),
      content: Joi.string(),
      description: Joi.string().allow('').optional(),
      linked: Joi.array().items(Joi.string().regex(/^[a-fA-F0-9]{24}$/)),
      block: Joi.array().items(
        Joi.object({
          block_type: Joi.string().valid(...Page.blockTypeOpts()),
          block_id: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
        })
      ),
    }),
  },

  getOrDelete: {
    params: Joi.object({
      id: Joi.string()
        .regex(/^[a-fA-F0-9]{24}$/)
        .required(),
    }),
  },

  patch: {
    params: Joi.object({
      id: Joi.string()
        .regex(/^[a-fA-F0-9]{24}$/)
        .required(),
    }),
    body: Joi.object({
      language: Joi.string().valid(...Page.langOpts()),
      title: Joi.string(),
      slug: Joi.string(),
      cover: Joi.string().allow('').optional(),
      content: Joi.string(),
      description: Joi.string().allow('').optional(),
      linked: Joi.array().items(Joi.string().regex(/^[a-fA-F0-9]{24}$/)),
      block: Joi.array().items(
        Joi.object({
          block_type: Joi.string().valid(...Page.blockTypeOpts()),
          block_id: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
        })
      ),
    }),
  },
};

export default pageValidation;
