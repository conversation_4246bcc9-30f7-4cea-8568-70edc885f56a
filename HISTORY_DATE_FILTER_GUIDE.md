# History Date Filter Guide

## Tổng quan
Hệ thống history đã được cập nhật để hỗ trợ truy vấn theo khoảng thời gian tạo (`created_at`) từ ngày đến ngày.

## Tính năng mới

### **Parameters mới:**
- `created_from`: <PERSON><PERSON><PERSON> bắt đầu (optional)
- `created_to`: <PERSON><PERSON><PERSON> kết thúc (optional)

### **Supported Date Formats:**
- `YYYY-MM-DD` (recommended): `2024-01-15`
- `YYYY-MM-DDTHH:mm:ss.sssZ` (ISO): `2024-01-15T00:00:00.000Z`
- `YYYY-MM-DD HH:mm:ss`: `2024-01-15 00:00:00`

## API Usage

### **1. Lấy history trong khoảng thời gian:**
```
GET /api/v1/history?created_from=2024-01-01&created_to=2024-01-31
```

### **2. Lấy history từ ngày cụ thể:**
```
GET /api/v1/history?created_from=2024-01-15
```

### **3. <PERSON><PERSON>y history đến ngày cụ thể:**
```
GET /api/v1/history?created_to=2024-01-31
```

### **4. Kết hợp với filters khác:**
```
GET /api/v1/history?created_from=2024-01-01&created_to=2024-01-31&customer=user_id&advisor=advisor_id
```

## Implementation Details

### **Model Interface Updates:**
```typescript
interface IHistoryModel extends Model<IHistoryDocument> {
  list({
    page,
    perPage,
    sort,
    customer,
    advisor,
    is_active,
    created_from,    // ✅ New
    created_to,      // ✅ New
  }: {
    page?: number;
    perPage?: number;
    sort?: string;
    customer?: string;
    advisor?: string;
    is_active?: boolean;
    created_from?: string | Date;  // ✅ New
    created_to?: string | Date;    // ✅ New
  }): any;
}
```

### **Date Processing Logic:**
```typescript
// Xử lý filter theo ngày tạo
if (created_from || created_to) {
  options.created_at = {};
  
  if (created_from) {
    const fromDate = typeof created_from === 'string' ? new Date(created_from) : created_from;
    fromDate.setHours(0, 0, 0, 0);  // Set to start of day
    options.created_at.$gte = fromDate;
  }
  
  if (created_to) {
    const toDate = typeof created_to === 'string' ? new Date(created_to) : created_to;
    toDate.setHours(23, 59, 59, 999);  // Set to end of day
    options.created_at.$lte = toDate;
  }
}
```

## MongoDB Query Examples

### **1. Basic date range query:**
```javascript
db.histories.find({
  created_at: {
    $gte: ISODate("2024-01-01T00:00:00.000Z"),
    $lte: ISODate("2024-01-31T23:59:59.999Z")
  }
}).sort({ created_at: -1 })
```

### **2. From date only:**
```javascript
db.histories.find({
  created_at: {
    $gte: ISODate("2024-01-15T00:00:00.000Z")
  }
}).sort({ created_at: -1 })
```

### **3. To date only:**
```javascript
db.histories.find({
  created_at: {
    $lte: ISODate("2024-01-31T23:59:59.999Z")
  }
}).sort({ created_at: -1 })
```

### **4. Combined with other filters:**
```javascript
db.histories.find({
  customer: ObjectId("customer_id"),
  advisor: ObjectId("advisor_id"),
  is_active: true,
  created_at: {
    $gte: ISODate("2024-01-01T00:00:00.000Z"),
    $lte: ISODate("2024-01-31T23:59:59.999Z")
  }
}).sort({ created_at: -1 })
```

## JavaScript Usage Examples

### **1. Node.js with axios:**
```javascript
const axios = require('axios');

// Lấy history tháng này
const thisMonth = await axios.get('/api/v1/history', {
  params: {
    created_from: '2024-01-01',
    created_to: '2024-01-31',
    page: 1,
    perPage: 50
  }
});

// Lấy history 7 ngày qua
const lastWeek = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
const response = await axios.get('/api/v1/history', {
  params: {
    created_from: lastWeek.toISOString().split('T')[0],
    created_to: new Date().toISOString().split('T')[0]
  }
});
```

### **2. Frontend JavaScript:**
```javascript
// Lấy history hôm nay
const today = new Date().toISOString().split('T')[0];
fetch(`/api/v1/history?created_from=${today}&created_to=${today}`)
  .then(response => response.json())
  .then(data => {
    console.log(`Found ${data.length} records today`);
  });

// Lấy history theo customer trong khoảng thời gian
const getCustomerHistory = async (customerId, fromDate, toDate) => {
  const params = new URLSearchParams({
    customer: customerId,
    created_from: fromDate,
    created_to: toDate
  });
  
  const response = await fetch(`/api/v1/history?${params}`);
  return response.json();
};
```

## Use Cases

### **1. Dashboard Analytics:**
```javascript
// Thống kê history theo ngày
const getDailyStats = async (month) => {
  const startOfMonth = new Date(month.getFullYear(), month.getMonth(), 1);
  const endOfMonth = new Date(month.getFullYear(), month.getMonth() + 1, 0);
  
  return await getHistory({
    created_from: startOfMonth.toISOString().split('T')[0],
    created_to: endOfMonth.toISOString().split('T')[0]
  });
};
```

### **2. Report Generation:**
```javascript
// Báo cáo hoạt động advisor theo tuần
const getWeeklyAdvisorReport = async (advisorId, weekStart) => {
  const weekEnd = new Date(weekStart.getTime() + 6 * 24 * 60 * 60 * 1000);
  
  return await getHistory({
    advisor: advisorId,
    created_from: weekStart.toISOString().split('T')[0],
    created_to: weekEnd.toISOString().split('T')[0]
  });
};
```

### **3. Data Export:**
```javascript
// Export history theo tháng
const exportMonthlyHistory = async (year, month) => {
  const startDate = new Date(year, month - 1, 1);
  const endDate = new Date(year, month, 0);
  
  return await getHistory({
    created_from: startDate.toISOString().split('T')[0],
    created_to: endDate.toISOString().split('T')[0],
    perPage: -1  // Get all records
  });
};
```

## Testing

### **Chạy test:**
```bash
node test-history-date-filter.js
```

### **Test cases:**
- ✅ Date range filtering
- ✅ From date only
- ✅ To date only
- ✅ Different date formats
- ✅ Combined with other filters
- ✅ Performance testing
- ✅ Edge cases

## Performance Considerations

### **1. Database Indexing:**
```javascript
// Recommended indexes
db.histories.createIndex({ "created_at": -1 })
db.histories.createIndex({ "customer": 1, "created_at": -1 })
db.histories.createIndex({ "advisor": 1, "created_at": -1 })
```

### **2. Query Optimization:**
- Sử dụng pagination cho large datasets
- Combine date filters với other indexed fields
- Avoid very large date ranges without pagination

### **3. Caching:**
```javascript
// Cache daily/weekly reports
const cacheKey = `history_${customerId}_${fromDate}_${toDate}`;
const cachedResult = await cache.get(cacheKey);
if (cachedResult) return cachedResult;

const result = await getHistory(params);
await cache.set(cacheKey, result, 3600); // Cache for 1 hour
```

## Error Handling

### **1. Invalid Date Format:**
```javascript
try {
  const result = await getHistory({
    created_from: 'invalid-date',
    created_to: '2024-01-31'
  });
} catch (error) {
  console.error('Invalid date format:', error.message);
}
```

### **2. Date Range Validation:**
```javascript
const validateDateRange = (fromDate, toDate) => {
  const from = new Date(fromDate);
  const to = new Date(toDate);
  
  if (from > to) {
    throw new Error('From date must be before to date');
  }
  
  const daysDiff = (to - from) / (1000 * 60 * 60 * 24);
  if (daysDiff > 365) {
    throw new Error('Date range cannot exceed 1 year');
  }
};
```

## Best Practices

1. **Always use pagination** for large date ranges
2. **Validate date inputs** on both client and server
3. **Use appropriate indexes** for better performance
4. **Cache frequent queries** (daily/weekly reports)
5. **Set reasonable limits** on date range size
6. **Handle timezone considerations** if needed

## Migration Notes

### **Backward Compatibility:**
- ✅ Existing API calls work unchanged
- ✅ New parameters are optional
- ✅ No breaking changes to response format

### **Upgrade Steps:**
1. Update model interfaces
2. Update controller to pass new parameters
3. Test with existing and new functionality
4. Update API documentation
