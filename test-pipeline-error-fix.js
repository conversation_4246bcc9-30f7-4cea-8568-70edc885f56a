// Test script để kiểm tra lỗi pipeline đã được sửa
const axios = require('axios');

// Cấu hình base URL
const BASE_URL = 'http://localhost:3000/api/v1/users';

// Mock token
const AUTH_TOKEN = 'your-admin-token-here';

const headers = {
  'Authorization': `Bearer ${AUTH_TOKEN}`,
  'Content-Type': 'application/json'
};

/**
 * Test pipeline error fixes
 */
async function testPipelineErrorFixes() {
  console.log('\n🔧 Test: Pipeline Error Fixes');
  console.log('=' .repeat(60));

  const testCases = [
    {
      name: 'Empty info options (should not cause error)',
      params: { role: 'customer' },
      expectedBehavior: 'Should use find + populate, no aggregation'
    },
    {
      name: 'Single info filter',
      params: { education_type: 'university' },
      expectedBehavior: 'Should use aggregation with proper info match'
    },
    {
      name: 'ObjectId advisor (valid format)',
      params: { advisor: '507f1f77bcf86cd799439011' },
      expectedBehavior: 'Should convert to ObjectId and match'
    },
    {
      name: 'Invalid ObjectId advisor',
      params: { advisor: 'invalid-objectid-format' },
      expectedBehavior: 'Should fallback to string comparison'
    },
    {
      name: 'Boolean true',
      params: { is_create_document: 'true' },
      expectedBehavior: 'Should convert to boolean true'
    },
    {
      name: 'Boolean false',
      params: { is_create_document: 'false' },
      expectedBehavior: 'Should convert to boolean false'
    },
    {
      name: 'Multiple info filters',
      params: { 
        education_type: 'university',
        advisor: '507f1f77bcf86cd799439011',
        is_create_document: 'true'
      },
      expectedBehavior: 'Should handle all conditions properly'
    },
    {
      name: 'Mixed user and info filters',
      params: { 
        name: 'test',
        role: 'customer',
        education_type: 'university'
      },
      expectedBehavior: 'Should filter users first, then info'
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n📝 Testing: ${testCase.name}`);
    console.log(`   Params: ${JSON.stringify(testCase.params)}`);
    console.log(`   Expected: ${testCase.expectedBehavior}`);

    try {
      const startTime = Date.now();

      // Test both list and count
      const [listResponse, countResponse] = await Promise.all([
        axios.get(BASE_URL, {
          headers,
          params: { ...testCase.params, page: 1, perPage: 10 }
        }),
        axios.get(`${BASE_URL}/count`, {
          headers,
          params: testCase.params
        })
      ]);

      const endTime = Date.now();
      const duration = endTime - startTime;

      const listData = listResponse.data.data || [];
      const totalCount = countResponse.data.data || countResponse.data;

      console.log(`   ✅ Success: List=${listData.length}, Count=${totalCount} (${duration}ms)`);

      // Validate results
      if (typeof totalCount === 'number' && totalCount >= 0) {
        console.log(`   ✅ Count is valid number`);
      } else {
        console.log(`   ❌ Count is invalid: ${totalCount}`);
      }

      if (totalCount >= listData.length) {
        console.log(`   ✅ Consistency: Count >= List length`);
      } else {
        console.log(`   ⚠️  Note: Count (${totalCount}) < List length (${listData.length}) - may be due to pagination`);
      }

    } catch (error) {
      console.log(`   ❌ Error: ${error.response?.data?.message || error.message}`);
      
      // Log more details for debugging
      if (error.response?.status) {
        console.log(`   📋 Status: ${error.response.status}`);
      }
      if (error.response?.data?.stack) {
        console.log(`   📋 Stack: ${error.response.data.stack.split('\n')[0]}`);
      }
    }
  }
}

/**
 * Test ObjectId handling specifically
 */
async function testObjectIdHandling() {
  console.log('\n🆔 Test: ObjectId Handling');
  console.log('=' .repeat(60));

  const objectIdTests = [
    {
      name: 'Valid ObjectId format',
      advisor: '507f1f77bcf86cd799439011',
      expectedBehavior: 'Should convert to ObjectId'
    },
    {
      name: 'Invalid ObjectId format',
      advisor: 'invalid-id',
      expectedBehavior: 'Should use as string'
    },
    {
      name: 'Short string',
      advisor: 'abc',
      expectedBehavior: 'Should use as string'
    },
    {
      name: 'Very long string',
      advisor: 'a'.repeat(50),
      expectedBehavior: 'Should use as string'
    },
    {
      name: 'Special characters',
      advisor: 'test@#$%',
      expectedBehavior: 'Should use as string'
    }
  ];

  for (const test of objectIdTests) {
    console.log(`\n📝 Testing: ${test.name}`);
    console.log(`   Advisor: "${test.advisor}"`);
    console.log(`   Expected: ${test.expectedBehavior}`);

    try {
      const response = await axios.get(`${BASE_URL}/count`, {
        headers,
        params: { advisor: test.advisor }
      });

      const count = response.data.data || response.data;
      console.log(`   ✅ Success: Count = ${count}`);

    } catch (error) {
      console.log(`   ❌ Error: ${error.response?.data?.message || error.message}`);
    }
  }
}

/**
 * Test pipeline structure validation
 */
async function testPipelineStructure() {
  console.log('\n🔄 Test: Pipeline Structure Validation');
  console.log('=' .repeat(60));

  console.log('📝 Fixed pipeline structure:');
  console.log(`
// ✅ AFTER: Proper conditional pipeline building
const pipeline = [
  { $match: userOptions },              // Always present
  { $lookup: { ... } },                 // Always present for info queries
  { $unwind: '$info_detail' },          // Always present for info queries
];

// Conditional info match stage
if (Object.keys(infoOptions).length > 0) {
  const infoMatchConditions = {};
  
  if (infoOptions.education_type) {
    infoMatchConditions['info_detail.education_type'] = infoOptions.education_type;
  }
  
  if (infoOptions.advisor) {
    try {
      infoMatchConditions['info_detail.advisor'] = new Types.ObjectId(infoOptions.advisor);
    } catch (error) {
      infoMatchConditions['info_detail.advisor'] = infoOptions.advisor;
    }
  }
  
  pipeline.push({ $match: infoMatchConditions });
}

// Continue with sort, pagination, etc.
  `);

  // Test with a complex query to validate pipeline
  const testParams = {
    name: 'test',
    role: 'customer',
    education_type: 'university',
    advisor: '507f1f77bcf86cd799439011',
    is_create_document: 'true'
  };

  console.log('\n📊 Testing complex pipeline...');
  console.log(`   Params: ${JSON.stringify(testParams)}`);

  try {
    const startTime = Date.now();

    const [listResponse, countResponse] = await Promise.all([
      axios.get(BASE_URL, {
        headers,
        params: { ...testParams, page: 1, perPage: 20 }
      }),
      axios.get(`${BASE_URL}/count`, {
        headers,
        params: testParams
      })
    ]);

    const endTime = Date.now();
    const duration = endTime - startTime;

    const listData = listResponse.data.data || [];
    const totalCount = countResponse.data.data || countResponse.data;

    console.log(`   ✅ Complex pipeline works: List=${listData.length}, Count=${totalCount} (${duration}ms)`);

    // Validate data structure
    if (listData.length > 0) {
      const firstItem = listData[0];
      const hasInfo = firstItem.info && typeof firstItem.info === 'object';
      console.log(`   ✅ Data structure: Info object present = ${hasInfo}`);
      
      if (hasInfo) {
        console.log(`   ✅ Info fields: education_type=${firstItem.info.education_type !== undefined}, advisor=${firstItem.info.advisor !== undefined}`);
      }
    }

  } catch (error) {
    console.log(`   ❌ Pipeline error: ${error.response?.data?.message || error.message}`);
  }
}

/**
 * Test error scenarios
 */
async function testErrorScenarios() {
  console.log('\n🚨 Test: Error Scenarios');
  console.log('=' .repeat(60));

  const errorTests = [
    {
      name: 'Null advisor',
      params: { advisor: null }
    },
    {
      name: 'Undefined education_type',
      params: { education_type: undefined }
    },
    {
      name: 'Empty string filters',
      params: { education_type: '', advisor: '', is_create_document: '' }
    },
    {
      name: 'Invalid boolean',
      params: { is_create_document: 'maybe' }
    }
  ];

  for (const test of errorTests) {
    console.log(`\n📝 Testing: ${test.name}`);

    try {
      const response = await axios.get(`${BASE_URL}/count`, {
        headers,
        params: test.params
      });

      const count = response.data.data || response.data;
      console.log(`   ✅ Handled gracefully: Count = ${count}`);

    } catch (error) {
      console.log(`   ⚠️  Error (may be expected): ${error.response?.data?.message || error.message}`);
    }
  }
}

/**
 * Chạy tất cả tests
 */
async function runAllTests() {
  console.log('🚀 Pipeline Error Fix Test Suite');
  console.log('=' .repeat(70));

  // Test 1: Pipeline error fixes
  await testPipelineErrorFixes();

  // Test 2: ObjectId handling
  await testObjectIdHandling();

  // Test 3: Pipeline structure
  await testPipelineStructure();

  // Test 4: Error scenarios
  await testErrorScenarios();

  console.log('\n🎉 All pipeline error fix tests completed!');
  
  console.log('\n📋 Key Fixes Applied:');
  console.log('   ✅ Conditional pipeline building (no empty reduce)');
  console.log('   ✅ Proper ObjectId handling with try-catch');
  console.log('   ✅ Explicit info match conditions building');
  console.log('   ✅ Same logic between list and countItem');
  console.log('   ✅ Graceful error handling');

  console.log('\n💡 Benefits:');
  console.log('   - No more pipeline building errors');
  console.log('   - Robust ObjectId conversion');
  console.log('   - Consistent behavior across functions');
  console.log('   - Better error handling');
  console.log('   - Cleaner code structure');
}

// Chạy tests
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testPipelineErrorFixes,
  testObjectIdHandling,
  testPipelineStructure,
  testErrorScenarios,
  runAllTests
};
