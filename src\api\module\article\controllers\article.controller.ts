// 15/5/2024

import { Request, Response, NextFunction } from 'express';
import Article from '../models/article.model';
import <PERSON><PERSON>r<PERSON><PERSON><PERSON> from '../../../middlewares/error';
import SuccessHandler from '../../../middlewares/success';

class ArticleController {
  public static async load(req: any, res: Response, next: NextFunction, id: string) {
    try {
      const article = await Article.get(id);
      req.locals = { article };
      return next();
    } catch (error) {
      return ErrorHandler.handler(error, req, res);
    }
  }

  public static async get(req: any, res: Response, next: NextFunction) {
    try {
      SuccessHandler.success(req.locals.article.transform(), req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async getSlug(req: any, res: Response, next: NextFunction) {
    try {
      const { slug } = req.params;
      const article = await Article.getSlug(slug);
      SuccessHandler.success(article, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async remove(req: any, res: Response, next: NextFunction) {
    try {
      const { article } = req.locals;
      const now = new Date();

      const deleted = Object.assign(article, {
        deleted_by: req.user._id,
        deleted_at: now,
        is_active: false,
      });
      await deleted.save();
      SuccessHandler.success(deleted, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async list(req: Request, res: Response, next: NextFunction) {
    try {
      const articles = await Article.list(req.query);
      const count = await Article.countItem(req.query);
      const transformed = articles.map((article: any) => article.transform());
      SuccessHandler.success({ total: count, docs: transformed }, req, res);
    } catch (error: any) {
      next(error);
    }
  }

  public static async update(req: any, res: Response, next: NextFunction) {
    try {
      const { article } = req.locals;
      const { body, user } = req;
      const now = new Date();
      body.updated_by = user._id;
      body.updated_at = now;

      const updated = Object.assign(article, body);
      await updated.save();
      SuccessHandler.success(updated, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async create(req: any, res: Response, next: NextFunction) {
    try {
      const { body, user } = req;
      const now = new Date();
      body.created_at_by = user._id;
      body.created_at = now;
      const article = new Article(body);
      await article.save();
      SuccessHandler.success(article, req, res);
    } catch (error) {
      next(error);
    }
  }
}

export default ArticleController;
