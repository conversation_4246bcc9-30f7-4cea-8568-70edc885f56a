import httpStatus from 'http-status';
import passport from 'passport';
import APIError from '../utils/APIError';
import { Request, Response, NextFunction } from 'express';
import User from '../module/user/models/user.model';

class Auth {
  public static ADMIN = ['admin'];
  public static USER = ['user'];
  public static LOGGED_USER = '_loggedUser';

  private static handleJWT =
    (req: Request, res: Response, next: NextFunction, roles: string[] | string) =>
    async (err: any, user: any, info: any) => {
      const error = err || info;
      // const logIn = (a: any, b?: any) => Promise.promisify(a, b);
      const logIn = Promise.promisify(req.logIn);
      const apiError = new APIError({
        message: error ? error.message : 'Unauthorized',
        status: httpStatus.UNAUTHORIZED,
        stack: error ? error.stack : undefined,
      });
      try {
        if (error || !user) throw error;
        // await logIn(user, { session: false });
      } catch (e: any) {
        return next(apiError);
      }

      if (roles === Auth.LOGGED_USER) {
        if (user.role !== 'admin' && req.params.userId !== user._id.toString()) {
          apiError.status = httpStatus.FORBIDDEN;
          apiError.message = 'Forbidden';
          return next(apiError);
        }
      } else if (!roles.includes(user.role)) {
        apiError.status = httpStatus.FORBIDDEN;
        apiError.message = 'Forbidden';
        return next(apiError);
      } else if (err || !user) {
        return next(apiError);
      }

      req.user = user;

      return next();
    };

  public static authorize =
    (roles: string[] = User.roles()) =>
    (req: Request, res: Response, next: NextFunction) => {
      console.error('HEADERS', req.headers);
      console.error('BODY', req.body);
      passport.authenticate('jwt', { session: false }, Auth.handleJWT(req, res, next, roles))(
        req,
        res,
        next
      );
    };
}

export default Auth;
