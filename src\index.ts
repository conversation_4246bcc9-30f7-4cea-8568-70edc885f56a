import Express from './config/express';
import { MongoDatabase } from './config/mongoose';
// import { sequelize } from './config/sequelize';

Promise = require('bluebird');

async function startApp() {
  try {
    MongoDatabase.init();
    Express.init();

    // await sequelize.authenticate();
    // console.log('✅ Kết nối Mysql thành công.');

    // await sequelize.sync({
    //   alter: false,
    //   force: false,
    // });
  } catch (error) {
    console.error('❌', error);
  }
}

startApp();

module.exports = Express;
