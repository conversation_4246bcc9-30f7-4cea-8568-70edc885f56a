// 16/5/2024
import { Schema, model, Document, Model, Types } from 'mongoose';
import { omitBy, isNil } from 'lodash';
import APIError from '../../../utils/APIError';
import httpStatus from 'http-status';
import Slug from 'slug';

const langOpts = ['vie', 'eng'];

interface ICategory {
  order: number;
  title: string;
  slug: string;
  icon: string;
  path: string;
  language: string;
  parent: Types.ObjectId;
  children: Types.ObjectId[];
  linked: Types.ObjectId[];

  is_active: boolean;
  created_by: Types.ObjectId;
  updated_by: Types.ObjectId;
  deleted_at: Date;
  deleted_by: Types.ObjectId;
}

interface ICategoryDocument extends ICategory, Document {
  transform(): {};
}

interface ICategoryModel extends Model<ICategoryDocument> {
  langOpts(): string[];
  get(id: string): any;
  list({
    page,
    perPage,
    sort,
    title,
    language,
    is_active,
  }: {
    page?: number;
    perPage?: number;
    sort?: string;
    title?: string;
    language?: string;
    is_active?: boolean;
  }): any;
  countItem({
    title, //
    language,
    is_active,
  }: {
    title?: string;
    language?: string;
    is_active?: boolean;
  }): any;
}

const categorySchema = new Schema<ICategoryDocument>(
  {
    order: {
      type: Number,
    },
    title: {
      type: String,
      required: true,
      maxlength: 128,
      trim: true,
    },
    slug: {
      type: String,
      trim: true,
      default: '',
    },
    icon: {
      type: String,
      trim: true,
      default: '',
    },
    path: {
      type: String,
      trim: true,
      default: '',
    },
    language: {
      type: String,
      enum: langOpts,
      default: 'vie',
    },
    parent: {
      type: Schema.Types.ObjectId,
      ref: 'Category',
    },
    linked: [
      {
        type: Schema.Types.ObjectId,
        ref: 'Category',
      },
    ],
    children: [
      {
        type: Schema.Types.ObjectId,
        ref: 'Category',
      },
    ],

    is_active: {
      type: Boolean,
      default: true,
    },
    created_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    updated_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    deleted_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    deleted_at: {
      type: Date,
    },
  },
  {
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  }
);

categorySchema.methods.transform = function (): {} {
  const transformed: { [index: string]: any } = {};
  const fields = [
    '_id', //
    'title',
    'slug',
    'icon',
    'path',
    'language',
    'children',
    'linked',
    'parent',

    'is_active',
    'created_at',
    'is_active',
    'delete_at',
  ];

  fields.forEach((field: any) => {
    transformed[field] = this[field];
  });

  return transformed;
};

categorySchema.statics.langOpts = () => langOpts;

categorySchema.statics.list = function ({
  page = 1,
  perPage = 30,
  sort,
  title,
  language,
  is_active = true,
  is_parent = true,
}: {
  page: number;
  perPage: number;
  sort: string;
  title: string;
  language: string;
  is_active: boolean;
  is_parent: boolean;
}) {
  try {
    let options = omitBy(
      {
        title: new RegExp(title || '', 'i'),
        language,
        is_active,
      },
      isNil
    );
    if (is_parent) {
      options = Object.assign(options, { parent: null });
    }
    const sortOpts = sort ? JSON.parse(sort) : { order: 1 };
    const result = this.find(options)
      .populate({
        // 'children', '_id title icon path language'
        path: 'children',
        select: '_id title icon path language',
        match: { is_active: true },
      })
      .sort(sortOpts);
    if (perPage > -1) {
      result.skip(perPage * (page - 1)).limit(perPage);
    }
    return result.exec();
  } catch (error) {
    throw error;
  }
};

categorySchema.statics.countItem = function ({
  title,
  language,
  is_active = true,
  is_parent = true,
}: {
  title: string;
  language: string;
  is_active: boolean;
  is_parent: boolean;
}) {
  try {
    let options = omitBy(
      {
        title: new RegExp(title || '', 'i'),
        language,
        is_active,
      },
      isNil
    );
    if (is_parent) {
      options = Object.assign(options, { parent: null });
    }
    return this.find(options).count().exec();
  } catch (error) {
    throw error;
  }
};

categorySchema.statics.get = async function (id: string) {
  try {
    const category = await this.findById(id).exec();
    if (category) {
      return category;
    }
    throw new APIError({
      message: 'Category does not exist',
      status: httpStatus.NOT_FOUND,
    });
  } catch (error) {
    throw error;
  }
};

categorySchema.pre<ICategoryDocument>('save', async function (next) {
  const doc = this;
  if (this.isModified('title')) {
    doc.slug = Slug(doc.title).toLowerCase();
  }
  if (this.isModified('linked')) {
    const currentCategory = await Category.findById(doc._id);
    if (currentCategory) {
      for (let j = 0; j < currentCategory.linked.length; j += 1) {
        const item = currentCategory.linked[j];
        await Category.updateOne({ _id: item }, { $pull: { linked: doc._id } });
      }
    }
    for (let i = 0; i < doc.linked.length; i += 1) {
      const item = doc.linked[i];
      await Category.updateOne({ _id: item }, { $push: { linked: doc._id } });
    }
  }

  if (this.isModified('parent')) {
    const currentCategory = await Category.findById(doc._id);
    if (currentCategory && currentCategory.parent) {
      await Category.updateOne({ _id: currentCategory.parent }, { $pull: { children: doc._id } });
    }
    await Category.updateOne({ _id: doc.parent }, { $push: { children: doc._id } });
  }
  next();
});
const Category = model<ICategoryDocument, ICategoryModel>('Category', categorySchema);

export default Category;
