# User List-Count Synchronization Fix

## Vấn đề ban đầu

### **Inconsistency giữa `list()` và `countItem()`:**

#### **`userSchema.statics.list()` sử dụng:**
```typescript
const result = this.find(options)
  .populate({
    path: 'info',
    select: '_id is_create_document education_type advisor',
    match: optionsInfo, // ✅ Populate với match condition
  })
  .sort(sortOpts);
```

#### **`userSchema.statics.countItem()` sử dụng (cũ):**
```typescript
const options = {
  // ❌ Trộn lẫn user fields và info fields
  'info.education_type': education_type,
  'info.advisor': advisor,
  // ...
};

// ❌ Aggregation logic không match với populate logic
const count = await this.aggregate([
  { $lookup: { from: 'customerinfos', ... } },
  { $unwind: '$info' },
  { $match: options }, // ❌ Logic khác với populate match
]);
```

### **Kết quả:**
- `list()` trả về 10 items
- `countItem()` trả về 15 items
- **Inconsistent pagination!**

## Giải pháp đã áp dụng

### **1. Đồng bộ Filter Logic:**

#### **Tách riêng user fields và info fields:**
```typescript
// ✅ User fields (giống list function)
const options = omitBy({
  name: new RegExp(name || '', 'i'),
  email,
  role,
  phone,
  is_active,
  gender,
}, isNil);

// ✅ Info fields (giống list function)
const optionsInfo = omitBy({
  education_type,
  advisor,
  is_create_document,
}, isNil);
```

### **2. Aggregation Pipeline tương đương Populate:**

#### **Khi có info filters:**
```typescript
const pipeline = [
  // Step 1: Match user fields trước
  { $match: options },
  
  // Step 2: Lookup info (tương đương populate)
  {
    $lookup: {
      from: 'customerinfos',
      localField: 'info',
      foreignField: '_id',
      as: 'info_detail',
    },
  },
  
  // Step 3: Unwind để filter
  {
    $unwind: {
      path: '$info_detail',
      preserveNullAndEmptyArrays: true,
    },
  },
  
  // Step 4: Match info conditions (tương đương populate match)
  { $match: infoMatchConditions },
  
  // Step 5: Count
  { $count: 'totalCount' },
];
```

#### **Khi không có info filters:**
```typescript
// ✅ Simple count cho performance tốt hơn
return this.find(options).countDocuments();
```

### **3. Xử lý Boolean Fields:**

#### **Consistent boolean handling:**
```typescript
// ✅ Xử lý is_create_document giống list function
if (is_create_document === 'true') {
  infoMatchConditions['info_detail.is_create_document'] = true;
} else if (is_create_document === 'false') {
  infoMatchConditions['info_detail.is_create_document'] = false;
}
```

## So sánh Before vs After

### **Before (Inconsistent):**
```typescript
// ❌ Mixed field types
const options = {
  name: new RegExp(name || '', 'i'),
  email,
  'info.education_type': education_type, // ❌ Wrong approach
  'info.advisor': advisor,               // ❌ Wrong approach
};

// ❌ Different aggregation logic
const count = await this.aggregate([
  { $lookup: {...} },
  { $unwind: '$info' },
  { $match: options }, // ❌ Not equivalent to populate match
]);
```

### **After (Consistent):**
```typescript
// ✅ Separated concerns
const options = { /* user fields only */ };
const optionsInfo = { /* info fields only */ };

// ✅ Equivalent to populate logic
const pipeline = [
  { $match: options },           // User filter first
  { $lookup: {...} },            // Join info
  { $unwind: {...} },            // Prepare for info filter
  { $match: infoMatchConditions }, // Info filter (equivalent to populate match)
  { $count: 'totalCount' },
];
```

## Testing Strategy

### **1. Synchronization Tests:**
```javascript
// Test với các filter combinations
const testCases = [
  { name: 'No filters', params: {} },
  { name: 'User fields only', params: { name: 'test', role: 'customer' } },
  { name: 'Info fields only', params: { education_type: 'university' } },
  { name: 'Mixed fields', params: { name: 'test', education_type: 'university' } },
];

// Verify: count >= list.length (với pagination)
```

### **2. Edge Cases:**
```javascript
const edgeCases = [
  { name: 'Boolean strings', params: { is_create_document: 'true' } },
  { name: 'Empty filters', params: { name: '', email: '' } },
  { name: 'Non-existent IDs', params: { advisor: 'invalid_id' } },
];
```

### **3. Performance Tests:**
```javascript
// So sánh performance giữa list và count
// Đảm bảo count không chậm hơn list quá nhiều
```

## Benefits

### **1. Data Consistency:**
- ✅ `list()` và `countItem()` trả về consistent results
- ✅ Pagination hoạt động chính xác
- ✅ UI hiển thị đúng total count

### **2. Code Maintainability:**
- ✅ Cùng logic filter trong cả 2 functions
- ✅ Dễ debug khi có issues
- ✅ Thay đổi logic chỉ cần update 1 nơi

### **3. Performance:**
- ✅ Optimized cho trường hợp không có info filters
- ✅ Aggregation chỉ khi cần thiết
- ✅ Proper indexing strategy

## Usage Examples

### **API Calls:**
```javascript
// Get users with pagination
GET /api/v1/users?page=1&perPage=10&education_type=university

// Get total count
GET /api/v1/users/count?education_type=university

// Results should be consistent:
// - List returns 10 items (or less on last page)
// - Count returns total matching items
// - Pagination calculation: totalPages = Math.ceil(count / perPage)
```

### **Frontend Integration:**
```javascript
const [users, setUsers] = useState([]);
const [totalCount, setTotalCount] = useState(0);

// Fetch both list and count
const fetchUsers = async (filters, page = 1) => {
  const [listResponse, countResponse] = await Promise.all([
    api.get('/users', { params: { ...filters, page, perPage: 10 } }),
    api.get('/users/count', { params: filters })
  ]);
  
  setUsers(listResponse.data.data);
  setTotalCount(countResponse.data.data);
  
  // ✅ Now pagination works correctly
  const totalPages = Math.ceil(totalCount / 10);
};
```

## Migration Notes

### **Backward Compatibility:**
- ✅ No API changes required
- ✅ Same parameters for both functions
- ✅ Same response format

### **Database Impact:**
- ✅ No schema changes needed
- ✅ Existing indexes still work
- ✅ Performance may improve for simple queries

## Monitoring

### **Key Metrics to Watch:**
1. **Consistency Check:** `list.length <= perPage` and `count >= list.length`
2. **Performance:** Count query should not be significantly slower than list
3. **Error Rate:** Both functions should have similar error rates

### **Alerts to Set Up:**
```javascript
// Alert if count and list are inconsistent
if (count < actualListLength) {
  alert('List-Count inconsistency detected');
}

// Alert if count query is too slow
if (countQueryTime > listQueryTime * 2) {
  alert('Count query performance issue');
}
```

## Future Improvements

1. **Caching:** Cache count results for expensive queries
2. **Indexing:** Add compound indexes for common filter combinations
3. **Aggregation Optimization:** Use $facet for getting both list and count in one query
4. **Real-time Updates:** Invalidate count cache when data changes

## Testing

Run the synchronization tests:
```bash
node test-user-list-count-sync.js
```

This will verify:
- ✅ List and count return consistent results
- ✅ All filter combinations work correctly
- ✅ Edge cases are handled properly
- ✅ Performance is acceptable
