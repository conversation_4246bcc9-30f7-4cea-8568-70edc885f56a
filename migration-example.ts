// Migration Example: Nodemailer vs Gmail API
// This file shows how to migrate from Nodemailer to Gmail API

// ============================================================================
// BEFORE: Using Nodemailer (mailer.ts)
// ============================================================================

/*
import Nodemailer from '../config/mailer';

// G<PERSON><PERSON> thông báo đăng ký
await Nodemailer.sendApplyNotification({
  email: '<EMAIL>',
  name: '<PERSON><PERSON>ễn Văn A',
  phone: '0123456789',
  education_type: '<PERSON><PERSON><PERSON> họ<PERSON>',
  major_interest_most: 'Công nghệ thông tin',
  now: new Date().toISOString()
});

// <PERSON><PERSON><PERSON> thông báo advisor
await Nodemailer.sendAdvisorNotification({
  email: '<EMAIL>',
  name: 'Trần Thị B',
  phone: '0987654321',
  advisor_name: 'Thầy Nguyễn Văn C'
});
*/

// ============================================================================
// AFTER: Using Gmail API (gmail.ts)
// ============================================================================

import GmailService from './src/config/gmail';

// Example 1: Gửi thông báo đăng ký (tương tự như cũ)
export async function sendApplyNotificationExample() {
  try {
    await GmailService.sendApplyNotification({
      email: '<EMAIL>',
      name: 'Nguyễn Văn A',
      phone: '0123456789',
      education_type: 'Đại học',
      major_interest_most: 'Công nghệ thông tin',
      now: new Date().toISOString()
    });
    console.log('✅ Apply notification sent successfully');
  } catch (error) {
    console.error('❌ Failed to send apply notification:', error);
  }
}

// Example 2: Gửi thông báo advisor (tương tự như cũ)
export async function sendAdvisorNotificationExample() {
  try {
    await GmailService.sendAdvisorNotification({
      email: '<EMAIL>',
      name: 'Trần Thị B',
      phone: '0987654321',
      advisor_name: 'Thầy Nguyễn Văn C'
    });
    console.log('✅ Advisor notification sent successfully');
  } catch (error) {
    console.error('❌ Failed to send advisor notification:', error);
  }
}

// ============================================================================
// NEW FEATURES: Chỉ có trong Gmail API
// ============================================================================

// Example 3: Gửi email đơn giản (tính năng mới)
export async function sendSimpleEmailExample() {
  try {
    await GmailService.sendSimpleEmail(
      '<EMAIL>',
      'Welcome to Tan Tao University',
      `
        <h1>Chào mừng bạn đến với Đại học Tân Tạo!</h1>
        <p>Cảm ơn bạn đã quan tâm đến chương trình đào tạo của chúng tôi.</p>
        <p>Chúng tôi sẽ liên hệ với bạn trong thời gian sớm nhất.</p>
        <br>
        <p>Trân trọng,<br>Đại học Tân Tạo</p>
      `
    );
    console.log('✅ Simple email sent successfully');
  } catch (error) {
    console.error('❌ Failed to send simple email:', error);
  }
}

// Example 4: Gửi email với CC và BCC (tính năng mới)
export async function sendEmailWithCCBCCExample() {
  try {
    await GmailService.sendEmail({
      to: '<EMAIL>',
      cc: '<EMAIL>',
      bcc: '<EMAIL>',
      subject: 'Thông báo kết quả xét tuyển',
      html: `
        <h2>Thông báo kết quả xét tuyển</h2>
        <p>Kính gửi bạn Nguyễn Văn A,</p>
        <p>Chúc mừng! Bạn đã trúng tuyển vào ngành Công nghệ thông tin.</p>
        <p>Vui lòng liên hệ phòng đào tạo để hoàn tất thủ tục nhập học.</p>
        <p>Thời gian: 8:00 AM - 5:00 PM, thứ 2 - thứ 6</p>
        <p>Địa chỉ: Đại học Tân Tạo, Quận 12, TP.HCM</p>
      `
    });
    console.log('✅ Email with CC/BCC sent successfully');
  } catch (error) {
    console.error('❌ Failed to send email with CC/BCC:', error);
  }
}

// Example 5: Gửi email hàng loạt (tính năng mới)
export async function sendBulkEmailsExample() {
  const students = [
    { email: '<EMAIL>', name: 'Nguyễn Văn A', major: 'Công nghệ thông tin' },
    { email: '<EMAIL>', name: 'Trần Thị B', major: 'Quản trị kinh doanh' },
    { email: '<EMAIL>', name: 'Lê Văn C', major: 'Y khoa' }
  ];

  const emails = students.map(student => ({
    to: student.email,
    subject: `Chúc mừng ${student.name} trúng tuyển ngành ${student.major}`,
    html: `
      <h2>Chúc mừng bạn đã trúng tuyển!</h2>
      <p>Kính gửi bạn ${student.name},</p>
      <p>Chúc mừng! Bạn đã trúng tuyển vào ngành <strong>${student.major}</strong>.</p>
      <p>Chúng tôi rất vui mừng chào đón bạn gia nhập đại gia đình Đại học Tân Tạo.</p>
      <p>Vui lòng theo dõi email để nhận thông tin chi tiết về thủ tục nhập học.</p>
      <br>
      <p>Trân trọng,<br>Ban tuyển sinh - Đại học Tân Tạo</p>
    `
  }));

  try {
    const results = await GmailService.sendBulkEmails(emails);
    
    // Xử lý kết quả
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    
    console.log(`✅ Bulk emails completed: ${successful} sent, ${failed} failed`);
    
    // Log chi tiết các email thất bại
    results.filter(r => !r.success).forEach(result => {
      console.error(`❌ Failed to send to ${result.to}: ${result.error}`);
    });
    
  } catch (error) {
    console.error('❌ Failed to send bulk emails:', error);
  }
}

// Example 6: Kiểm tra kết nối và thông tin Gmail (tính năng mới)
export async function checkGmailStatusExample() {
  try {
    // Kiểm tra kết nối
    const isConnected = await GmailService.testConnection();
    if (!isConnected) {
      console.log('❌ Gmail API connection failed');
      return;
    }

    // Lấy thông tin profile
    const profile = await GmailService.getProfile();
    console.log('📧 Gmail Profile:', {
      email: profile.emailAddress,
      totalMessages: profile.messagesTotal,
      totalThreads: profile.threadsTotal
    });

    // Lấy danh sách labels
    const labels = await GmailService.getLabels();
    console.log('🏷️ Available Labels:', labels.slice(0, 5).map(l => l.name));

  } catch (error) {
    console.error('❌ Failed to check Gmail status:', error);
  }
}

// ============================================================================
// MIGRATION CHECKLIST
// ============================================================================

/*
✅ Migration Checklist:

1. Environment Variables:
   - MAILER_CLIENT_ID ✓
   - MAILER_CLIENT_SECRET ✓
   - MAILER_REDIRECT_URI ✓
   - MAILER_REFRESH_TOKEN ✓
   - MAILER_EMAIL ✓
   - MAILER_APPLY_NOTIFICATION_ADDRESS ✓

2. Code Changes:
   - Replace import statements ✓
   - Update method calls ✓
   - Add error handling ✓
   - Test new features ✓

3. Testing:
   - Test connection ✓
   - Test simple emails ✓
   - Test template emails ✓
   - Test bulk emails ✓
   - Test error scenarios ✓

4. Deployment:
   - Update production environment variables ✓
   - Monitor email sending ✓
   - Check Gmail API quotas ✓
   - Verify email delivery ✓

5. Benefits After Migration:
   - Better performance ✓
   - More features (CC/BCC, bulk sending) ✓
   - Better error handling ✓
   - Direct Gmail API access ✓
   - Reduced dependencies ✓
*/

// Example usage function
export async function runAllExamples() {
  console.log('🚀 Running Gmail API Examples...\n');

  await sendApplyNotificationExample();
  await sendAdvisorNotificationExample();
  await sendSimpleEmailExample();
  await sendEmailWithCCBCCExample();
  await sendBulkEmailsExample();
  await checkGmailStatusExample();

  console.log('\n✅ All examples completed!');
}
