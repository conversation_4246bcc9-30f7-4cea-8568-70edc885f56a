// 23/4/2024
import multer from 'multer';
import fs from 'fs';
import path from 'path';
import { Request, Response, NextFunction } from 'express';
import CustomerInfoOption from '../models/customer.info.option.model';
import <PERSON>rror<PERSON>andler from '../../../middlewares/error';
import <PERSON><PERSON>andler from '../../../middlewares/success';
import Vars from '../../../../config/vars';
import validation from '../validations/customer.info.option.validation';
import APIError from '../../../utils/APIError';
import httpStatus from 'http-status';
import Xlsx from '../../../../config/xlsx';

const { uploadDir } = Vars.config();

class CustomerInfoOptionController {
  public static async load(req: any, res: Response, next: NextFunction, id: string) {
    try {
      const option = await CustomerInfoOption.get(id);
      req.locals = { option };
      return next();
    } catch (error) {
      return ErrorHandler.handler(error, req, res);
    }
  }

  public static async get(req: any, res: Response, next: NextFunction) {
    try {
      SuccessHandler.success(req.locals.option.transform(), req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async remove(req: any, res: Response, next: NextFunction) {
    try {
      const { option } = req.locals;
      const now = new Date();

      const deleted = Object.assign(option, {
        deleted_by: req.user._id,
        deleted_at: now,
        is_active: false,
      });
      await deleted.save();
      SuccessHandler.success(deleted, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async list(req: Request, res: Response, next: NextFunction) {
    try {
      const options = await CustomerInfoOption.list(req.query);
      const count = await CustomerInfoOption.countItem(req.query);
      const transformed = options.map((option: any) => option.transform());
      SuccessHandler.success({ total: count, docs: transformed }, req, res);
    } catch (error: any) {
      next(error);
    }
  }

  public static async all(req: Request, res: Response, next: NextFunction) {
    try {
      const options = await CustomerInfoOption.all();
      SuccessHandler.success(options, req, res);
    } catch (error: any) {
      next(error);
    }
  }

  public static async update(req: any, res: Response, next: NextFunction) {
    try {
      const { option } = req.locals;
      const { body, user } = req;
      const now = new Date();
      body.updated_by = user._id;
      body.updated_at = now;

      const updated = Object.assign(option, body);
      await updated.save();
      SuccessHandler.success(updated, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async create(req: any, res: Response, next: NextFunction) {
    try {
      const { body, user } = req;
      const now = new Date();
      body.created_at_by = user._id;
      body.created_at = now;
      const option = new CustomerInfoOption(body);
      await option.save();
      SuccessHandler.success(option, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async import(req: any, res: Response, next: NextFunction) {
    try {
      const { body, user } = req;
      const now = new Date();
      for (let i = 1; i < body.value.length; i += 1) {
        const item = body.value[i];
        const option = new CustomerInfoOption({
          key: body.key,
          value: item,
          name: body.name,
          created_at: now,
          created_at_by: user._id,
        });
        await option.save();
      }
      SuccessHandler.success({ body }, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async importFromExcel(req: any, res: Response, next: NextFunction) {
    const fileStorage = multer.diskStorage({
      destination: (rq: any, file, cb: any) => {
        if (!fs.existsSync(uploadDir.upload)) {
          fs.mkdirSync(uploadDir.upload);
          if (!fs.existsSync(uploadDir.tmp)) {
            fs.mkdirSync(uploadDir.tmp);
          }
        }
        cb(null, path.join(process.cwd(), uploadDir.upload));
      },
      filename: (rq: any, file, cb: any) => {
        const datetimestamp = Date.now();
        cb(
          null,
          `${file.fieldname}-${datetimestamp}.${
            file.originalname.split('.')[file.originalname.split('.').length - 1]
          }`
        );
      },
    });

    const fileFilter = (req: any, file: any, cb: any) => {
      if (
        file.mimetype === 'application/vnd.ms-excel' ||
        file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ) {
        cb(null, true);
      } else {
        cb(new Error('File format should be Excel file'), false); // if validation failed then generate error
      }
    };

    const upload = multer({
      storage: fileStorage,
      fileFilter,
    }).single('file');

    upload(req, res, async (err) => {
      try {
        const { error, value } = validation.importFromExcel.validate(req.body);

        if (err) {
          throw new APIError({
            message: err.message,
            status: httpStatus.BAD_REQUEST,
          });
        }
        if (error) {
          // Trả về lỗi nếu dữ liệu không hợp lệ
          throw new APIError({
            message: error.details[0].message,
            status: httpStatus.BAD_REQUEST,
          });
        }

        const xlData = Xlsx.readData(req.file.path);
        let key = '';
        let name = '';
        for (let i = 0; i < xlData.length; i += 1) {
          const item: any = xlData[i];
          if (i === 0) {
            key = item.key;
            name = item.name;
          }
          const option = new CustomerInfoOption({
            key,
            name,
            value: item.value,
          });
          await option.save();
        }
        SuccessHandler.success({}, req, res);
      } catch (error) {
        next(error);
      }
    });
  }
}

export default CustomerInfoOptionController;
