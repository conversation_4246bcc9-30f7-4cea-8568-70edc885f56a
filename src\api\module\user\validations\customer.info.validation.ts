// 2/5/2024
import Jo<PERSON>, { object } from 'joi';
import CustomerInfo from '../models/customer.info.model';

const customerInfoOptionValidation = {
  list: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      is_active: Joi.boolean(),
    }),
  },

  create: {
    body: Joi.object({
      user: Joi.string()
        .regex(/^[a-fA-F0-9]{24}$/)
        .required(),
      data_created_at: Joi.date(),
      data_updated_at: Joi.date(),
      next_care_at: Joi.date(),
      advisor: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      channel: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      school: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      class: Joi.string().allow('').optional(),
      identify: Joi.string().allow('').optional().max(12).min(12),
      province: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      district: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      major_interest_most: Joi.array().items(Joi.string().regex(/^[a-fA-F0-9]{24}$/)),
      major_interest: Joi.string().allow('').optional(),
      city_interest: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      level_interest: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      university_interest: Joi.string().allow('').optional(),
      data_source: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      introducer: Joi.string().allow('').optional(),
      is_know_ttu: Joi.boolean(),
      know_channel: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      is_apply_ttu: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      is_open_house: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      is_uni_prep: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      fee_reduction_type: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      fee_reduction_policy: Joi.string().allow('').optional(),
      admission_scholarship: Joi.string().allow('').optional(),
      talent_scholarship: Joi.boolean(),
      data_status: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      ttu_target: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      moet_target: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      why_ttu: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      why_not_ttu: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      note: Joi.string().allow('').optional(),
      object: Joi.string().valid(...CustomerInfo.objectOpts()),
      //update
      private_candidate_note: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      ec: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      registration_number: Joi.string().allow('').optional(),
      consultation_phase: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      priority_area: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      family_background: Joi.string().allow('').optional(),
      parents_name: Joi.string().allow('').optional(),
      parents_number: Joi.string().allow('').optional(),
      parents_job: Joi.string().allow('').optional(),
    }),
  },

  getOrDelete: {
    params: Joi.object({
      id: Joi.string()
        .regex(/^[a-fA-F0-9]{24}$/)
        .required(),
    }),
  },

  patch: {
    params: Joi.object({
      id: Joi.string()
        .regex(/^[a-fA-F0-9]{24}$/)
        .required(),
    }),
    body: Joi.object({
      identify: Joi.string().allow('').optional().max(12).min(12),
      user: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      data_created_at: Joi.date(),
      data_updated_at: Joi.date(),
      next_care_at: Joi.date(),
      advisor: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      channel: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      school: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      class: Joi.string().allow('').optional(),
      province: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      district: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      major_interest_most: Joi.array().items(Joi.string().regex(/^[a-fA-F0-9]{24}$/)),
      major_interest: Joi.string().allow('').optional(),
      city_interest: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      level_interest: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      university_interest: Joi.string().allow('').optional(),
      data_source: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      introducer: Joi.string().allow('').optional(),
      is_know_ttu: Joi.boolean(),
      know_channel: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      is_apply_ttu: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      is_open_house: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      is_uni_prep: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      fee_reduction_type: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      fee_reduction_policy: Joi.string().allow('').optional(),
      admission_scholarship: Joi.string().allow('').optional(),
      talent_scholarship: Joi.boolean(),
      data_status: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      ttu_target: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      moet_target: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      why_ttu: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      why_not_ttu: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      note: Joi.string().allow('').optional(),
      object: Joi.string().valid(...CustomerInfo.objectOpts()),
       //update
      private_candidate_note: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      ec: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      registration_number: Joi.string().allow('').optional(),
      consultation_phase: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      priority_area: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      family_background: Joi.string().allow('').optional(),
      parents_name: Joi.string().allow('').optional(),
      parents_number: Joi.string().allow('').optional(),
      parents_job: Joi.string().allow('').optional(),
    }),
  },
};

export default customerInfoOptionValidation;
