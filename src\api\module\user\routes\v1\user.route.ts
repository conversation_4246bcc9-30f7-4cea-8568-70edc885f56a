import { Router } from 'express';
import { validate } from 'express-validation';
import controller from '../../controllers/user.controller';
import validation from '../../validations/user.validation';
import Auth from '../../../../middlewares/auth';

const router = Router();
router.param('id', controller.load);

router
  .route('/') //
  .get(Auth.authorize([...Auth.ADMIN, ...Auth.USER]), validate(validation.list), controller.list)
  .post(
    Auth.authorize([...Auth.ADMIN, ...Auth.USER]),
    validate(validation.create),
    controller.create
  );

router
  .route('/profile')
  .get(Auth.authorize(), controller.loggedIn)
  .patch(Auth.authorize(), validate(validation.updateProfile), controller.updateProfile);

router
  .route('/admission') //
  .post(validate(validation.admission), controller.admission);

router
  .route('/export-excel') //
  .get(Auth.authorize([...Auth.ADMIN, ...Auth.USER]), controller.exportExcel);

router
  .route('/:id') //
  .get(
    Auth.authorize([...Auth.ADMIN, ...Auth.USER]),
    validate(validation.getOrDelete),
    controller.get
  )
  .patch(
    Auth.authorize([...Auth.ADMIN, ...Auth.USER]),
    validate(validation.updateProfile),
    controller.patch
  )
  .delete(
    Auth.authorize([...Auth.ADMIN, ...Auth.USER]),
    validate(validation.getOrDelete),
    controller.remove
  );

export default router;
