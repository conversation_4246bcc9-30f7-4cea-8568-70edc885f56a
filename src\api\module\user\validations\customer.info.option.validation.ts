// 23/4/2024
import Jo<PERSON> from 'joi';

const customerInfoOptionValidation = {
  list: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      is_active: Joi.boolean(),
      key: Joi.string(),
    }),
  },

  create: {
    body: Joi.object({
      key: Joi.string().required(),
      value: Joi.string().required(),
      note: Joi.string(),
      name: Joi.string(),
    }),
  },

  getOrDelete: {
    params: Joi.object({
      id: Joi.string()
        .regex(/^[a-fA-F0-9]{24}$/)
        .required(),
    }),
  },

  patch: {
    params: Joi.object({
      id: Joi.string()
        .regex(/^[a-fA-F0-9]{24}$/)
        .required(),
    }),
    body: Joi.object({
      key: Joi.string(),
      value: Joi.string(),
      note: Joi.string().allow('').optional(),
    }),
  },

  importFromExcel: Joi.object({
    file: Joi.string(),
  }),
};

export default customerInfoOptionValidation;
