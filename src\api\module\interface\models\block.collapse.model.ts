// 5/4/2024
import { Schema, model, Document, Model, Types } from 'mongoose';
import { omitBy, isNil } from 'lodash';
import APIError from '../../../utils/APIError';
import httpStatus from 'http-status';

interface IBlockCollapse {
  order: number;
  title: string;
  content: string;
  parent: Types.ObjectId;
  children: Types.ObjectId[];

  is_active: boolean;
  created_by: Types.ObjectId;
  updated_by: Types.ObjectId;
  deleted_at: Date;
  deleted_by: Types.ObjectId;
}

interface IBlockCollapseDocument extends I<PERSON>lockCollapse, Document {
  transform(): {};
}

interface IBlockCollapseModel extends Model<IBlockCollapseDocument> {
  langOpts(): string[];
  get(id: string): any;
  list({
    page,
    perPage,
    sort,
    title,
    language,
    is_active,
  }: {
    page?: number;
    perPage?: number;
    sort?: string;
    title?: string;
    language?: string;
    is_active?: boolean;
  }): any;
  countItem({
    title, //
    language,
    is_active,
  }: {
    title?: string;
    language?: string;
    is_active?: boolean;
  }): any;
}

const blockCollapseSchema = new Schema<IBlockCollapseDocument>(
  {
    order: {
      type: Number,
    },
    title: {
      type: String,
      required: true,
      trim: true,
    },
    content: {
      type: String,
      trim: true,
    },
    parent: {
      type: Schema.Types.ObjectId,
      ref: 'BlockCollapse',
    },
    children: [
      {
        type: Schema.Types.ObjectId,
        ref: 'BlockCollapse',
      },
    ],
    is_active: {
      type: Boolean,
      default: true,
    },
    created_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    updated_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    deleted_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    deleted_at: {
      type: Date,
    },
  },
  {
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  }
);

blockCollapseSchema.methods.transform = function (): {} {
  const transformed: { [index: string]: any } = {};
  const fields = [
    '_id', //
    'title',
    'content',
    'children',

    'is_active',
    'created_at',
    'is_active',
    'delete_at',
  ];

  fields.forEach((field: any) => {
    transformed[field] = this[field];
  });

  return transformed;
};

blockCollapseSchema.statics.list = function ({
  page = 1,
  perPage = 30,
  sort,
  title,
  content,
  is_parent = true,
  is_active = true,
}: {
  page: number;
  perPage: number;
  sort: string;
  title: string;
  content: string;
  is_active: boolean;
  is_parent: boolean;
}) {
  try {
    let options = omitBy(
      {
        title: new RegExp(title || '', 'i'),
        content: new RegExp(content || '', 'i'),
        is_active,
      },
      isNil
    );
    if (is_parent) {
      options = Object.assign(options, { parent: null });
    }
    const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };
    const result = this.find(options)
      .populate({
        path: 'children',
        select: '_id title content order',
        match: { is_active: true },
        options: { sort: { order: 1 } },
      })
      .sort(sortOpts);
    if (perPage > -1) {
      result.skip(perPage * (page - 1)).limit(perPage);
    }
    return result.exec();
  } catch (error) {
    throw error;
  }
};

blockCollapseSchema.statics.countItem = function ({
  title,
  content,
  is_active = true,
  is_parent = true,
}: {
  title: string;
  content: string;
  is_active: boolean;
  is_parent: boolean;
}) {
  try {
    let options = omitBy(
      {
        title: new RegExp(title || '', 'i'),
        content: new RegExp(content || '', 'i'),
        is_active,
      },
      isNil
    );
    if(is_parent){
      options = Object.assign(options, { parent: null });
    }
    return this.find(options).count().exec();
  } catch (error) {
    throw error;
  }
};

blockCollapseSchema.statics.get = async function (id: string) {
  try {
    const blockCollapse = await this.findById(id)
      .populate({
        path: 'children',
        select: '_id title content order',
        match: { is_active: true },
        options: { sort: { order: 1 } },
      })
      .exec();
    if (blockCollapse) {
      return blockCollapse;
    }
    throw new APIError({
      message: 'BlockCollapse does not exist',
      status: httpStatus.NOT_FOUND,
    });
  } catch (error) {
    throw error;
  }
};

blockCollapseSchema.pre<IBlockCollapseDocument>('save', async function (next) {
  const doc = this;
  if (this.isModified('parent')) {
    const currentBlockCollapse = await BlockCollapse.findById(doc._id);
    if (currentBlockCollapse && currentBlockCollapse.parent) {
      await BlockCollapse.updateOne(
        { _id: currentBlockCollapse.parent },
        { $pull: { children: doc._id } }
      );
    }
    await BlockCollapse.updateOne({ _id: doc.parent }, { $push: { children: doc._id } });
  }
  next();
});

const BlockCollapse = model<IBlockCollapseDocument, IBlockCollapseModel>(
  'BlockCollapse',
  blockCollapseSchema
);

export default BlockCollapse;
