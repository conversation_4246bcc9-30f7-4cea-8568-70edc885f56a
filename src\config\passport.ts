import { Application, Response, Request, NextFunction } from 'express';
import passport from 'passport';
import { ExtractJwt } from 'passport-jwt';
import {Strategy as JwtStrategy} from 'passport-jwt'
import Vars from './vars';

import LocalStrategy from '../api/services/strategies/local';
import User from '../api/module/user/models/user.model';

class Passport {
  readonly jwtOptions = {
    secretOrKey: Vars.config().jwtSecret,
    jwtFromRequest: ExtractJwt.fromAuthHeaderWithScheme('Bearer'),
  };
  private async jwt(payload: any, done: any) {
    try {
      const user = await User.findById(payload.sub);
      if (user) return done(null, user);
      return done(null, false);
    } catch (error) {
      return done(error, false);
    }
  }
  public mountPackage(express: Application): Application {
    express.use(passport.initialize());
    // express.use(passport.session());
    passport.use('jwt', new JwtStrategy(this.jwtOptions, this.jwt));

    passport.serializeUser<any, any>((user, done: any) => {
      done(null, user.id);
    });

    passport.deserializeUser<any, any>((id, done) => {
      User.findById(id, (err: any, user: any) => {
        done(err, user);
      });
    });

    this.mountLocalStrategies();

    return express;
  }

  public mountLocalStrategies(): void {
    try {
      LocalStrategy.init(passport);
    } catch (err: any) {
      console.log(err.message);
    }
  }

  public isAuthenticated(req: Request, res: Response, next: NextFunction): any {
    if (req.isAuthenticated()) {
      return next();
    }

    return res.send({ status: 'errors', msg: 'Please Log-In to access any further!' });
  }

  // public isAuthorized(req: Request, res: Response, next: NextFunction): any {
  //   const provider = req.path.split('/').slice(-1)[0];
  //   const token = req.user.tokens.find((token) => token.kind === provider);
  //   if (token) {
  //     return next();
  //   } else {
  //     return res.redirect(`/auth/${provider}`);
  //   }
  // }
}

export default new Passport();
