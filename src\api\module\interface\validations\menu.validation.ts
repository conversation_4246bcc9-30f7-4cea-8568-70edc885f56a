import Joi from 'joi';
import Menu from '../models/menu.model';

const menuValidation = {
  list: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      language: Joi.string().valid(...Menu.langOpts()),
      is_active: Joi.boolean(),
      title: Joi.string(),
    }),
  },

  create: {
    body: Joi.object({
      language: Joi.string().valid(...Menu.langOpts()),
      title: Joi.string().required(),
      icon: Joi.string().allow('').optional(),
      path: Joi.string(),
      children: Joi.array().items(Joi.string().regex(/^[a-fA-F0-9]{24}$/)),
      linked: Joi.array().items(Joi.string().regex(/^[a-fA-F0-9]{24}$/)),
      parent: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
    }),
  },

  getOrDelete: {
    params: Joi.object({
      id: Joi.string()
        .regex(/^[a-fA-F0-9]{24}$/)
        .required(),
    }),
  },

  patch: {
    params: Joi.object({
      id: Joi.string()
        .regex(/^[a-fA-F0-9]{24}$/)
        .required(),
    }),
    body: Joi.object({
      language: Joi.string().valid(...Menu.langOpts()),
      title: Joi.string(),
      slug: Joi.string(),
      icon: Joi.string().allow('').optional(),
      path: Joi.string(),
      children: Joi.array().items(Joi.string().regex(/^[a-fA-F0-9]{24}$/)),
      linked: Joi.array().items(Joi.string().regex(/^[a-fA-F0-9]{24}$/)),
      parent: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
    }),
  },
};

export default menuValidation;
