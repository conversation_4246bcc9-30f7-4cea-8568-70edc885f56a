// 8/4/2024
import { Schema, model, Document, Model, Types } from 'mongoose';
import { omitBy, isNil } from 'lodash';
import APIError from '../../../utils/APIError';
import httpStatus from 'http-status';

const types = ['avatar', 'post', 'post_content','origin'];

interface IGallery {
  name: string;
  description: string;
  dir: string;
  link: string;
  link_preview: string;
  type: string;

  is_active: boolean;
  created_by: Types.ObjectId;
  updated_by: Types.ObjectId;
  deleted_at: Date;
  deleted_by: Types.ObjectId;
}

interface IGalleryDocument extends IGallery, Document {
  transform(): {};
}

interface IGalleryModel extends Model<IGalleryDocument> {
  get(id: string): any;
  list({
    page,
    perPage,
    sort,
    dir,
    name,
  }: {
    page?: number;
    perPage?: number;
    sort?: string;
    dir?: string;
    name?: string;
  }): any;
  countItem({ dir }: { dir?: string }): any;
  types(): [];
}

const gallerySchema = new Schema<IGalleryDocument>(
  {
    name: {
      type: String,
      required: true,
      maxlength: 128,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    dir: {
      type: String,
      required: true,
      maxlength: 128,
      trim: true,
    },
    type: {
      type: String,
      enum: types,
    },
    link: { type: String },
    link_preview: { type: String },
    is_active: {
      type: Boolean,
      default: true,
    },
    created_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    updated_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  {
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  }
);

gallerySchema.methods.transform = function (): {} {
  const transformed: { [index: string]: any } = {};
  const fields = [
    '_id', //
    'name',
    'description',
    'dir',
    'link',
    'link_preview',
    'created_at',
  ];

  fields.forEach((field: any) => {
    transformed[field] = this[field];
  });

  return transformed;
};

gallerySchema.statics.list = function ({
  page = 1,
  perPage = 30,
  sort,
  dir,
  name,
}: {
  page: number;
  perPage: number;
  sort: string;
  dir: string;
  name: string;
}) {
  try {
    const options = omitBy(
      {
        dir,
        name: new RegExp(name || '', 'i'),
      },
      isNil
    );
    const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };
    const result = this.find(options).sort(sortOpts);
    if (perPage > -1) {
      result.skip(perPage * (page - 1)).limit(perPage);
    }
    return result.exec();
  } catch (error) {
    throw error;
  }
};

gallerySchema.statics.countItem = function ({ dir, name }: { dir: string; name: string }) {
  try {
    const options = omitBy(
      {
        dir,
        name: new RegExp(name || '', 'i'),
      },
      isNil
    );
    return this.find(options).count().exec();
  } catch (error) {
    throw error;
  }
};

gallerySchema.statics.get = async function (id: string) {
  try {
    const gallery = await this.findById(id).exec();
    if (gallery) {
      return gallery;
    }
    throw new APIError({
      message: 'Image does not exist',
      status: httpStatus.NOT_FOUND,
    });
  } catch (error) {
    throw error;
  }
};

gallerySchema.statics.types = () => types;

const Gallery = model<IGalleryDocument, IGalleryModel>('Gallery', gallerySchema);

export default Gallery;
