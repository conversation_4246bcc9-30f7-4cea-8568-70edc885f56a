import axios from 'axios';
const baseURL = 'https://dktuyensinh.ttu.edu.vn/api/';

const api = axios.create({
  baseURL,
  timeout: 5000,
});

const createUser = async (data: {}): Promise<any> => {
  const res = await api.post('/users', data);
  return res.data;
};

const data = {
  cmnd: '001020211',
  diachi: '06',
  dienthoai: '0123456789',
  email: '<EMAIL>',
  gioitinh: '1',
  hoten: '<PERSON><PERSON> K<PERSON>á 7',
};

createUser(data)