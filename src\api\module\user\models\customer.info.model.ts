// 17/4/2024
import APIError from '../../../utils/APIError';
import httpStatus from 'http-status';
import { Schema, model, Document, Model, Types } from 'mongoose';
import { omitBy, isNil } from 'lodash';
import User from './user.model';
import GmailService from '../../../../config/gmail';

const objectOpts = ['Chính quy', '<PERSON>ên thông', 'Th<PERSON>c sĩ', 'Từ xa'];

interface ICustomerInfo {
  user: Types.ObjectId;
  identify: string; //Số CCCD
  cellular_network: Types.ObjectId; //Mạng di động
  data_created_at: Date; //Ngày tiếp cận Data
  data_updated_at: Date; //Ngày cập nhật tình trạng Data
  next_care_at: Date; //Ngày chăm sóc tiếp theo
  advisor: Types.ObjectId; //Tư vấn viên
  channel: Types.ObjectId; //Kênh Chăm sóc Data
  school: Types.ObjectId; //Trường THPT
  class: string; //Lớp
  province: Types.ObjectId; //Tỉnh/Thành phố
  district: Types.ObjectId; //Thành phố/Thị Xã/Huyện
  major_interest_most: Types.ObjectId; //Ngành học quan tâm nhất
  major_interest: string; //Ngành quan tâm khác
  city_interest: Types.ObjectId; //Chọn học Đại học tại Tỉnh/Thành phố
  level_interest: Types.ObjectId; //Lựa chọn cấp học sau tốt nghiệp THPT
  university_interest: string; //Trường Đại học đang quan tâm
  data_source: Types.ObjectId; //Nguồn thu Data
  introducer: string; //Thông tin người giới thiệu
  is_know_ttu: boolean; //Đã biết đến TTU
  know_channel: Types.ObjectId; //Đã biết đến TTU qua Kênh
  is_apply_ttu: Types.ObjectId; //Bạn quan tâm và Nộp HS vào TTU
  is_open_house: Types.ObjectId; //Tham gia Open House
  is_uni_prep: Types.ObjectId; //Tham gia UniPrep
  fee_reduction_type: Types.ObjectId; //Đối tượng
  fee_reduction_policy: string; //Chính sách giảm học phí
  admission_scholarship: string; //Học bổng Tuyển sinh
  talent_scholarship: boolean; //Học bổng tài năng toàn phần
  data_status: Types.ObjectId; //Kết quả tình trạng Data
  ttu_target: Types.ObjectId; //Ưu tiên TTU
  moet_target: Types.ObjectId; //Nguyện vọng MOET
  why_ttu: Types.ObjectId; //Lý do chọn học tại TTU
  why_not_ttu: Types.ObjectId; //Lý do không chọn học TTU
  note: string; //Ghi chú
  education_type: string; // Đối tượng
  is_create_document: boolean; // Chuyển qua my-tuyensinh
  //update
  private_candidate_note: Types.ObjectId; //Thí sinh tự do
  ec: Types.ObjectId; //EC
  registration_number: string; //Số báo danh
  consultation_phase: Types.ObjectId; //Giai đoạn tư vấn
  priority_area: Types.ObjectId; //Khu vực ưu tiên
  family_background: string; //Chi tiết Hoàn cảnh gia đình
  parents_name: string; //Họ & Tên Phụ huynh
  parents_number: string; //Số điện thoại Phụ huynh
  parents_job: string; //Nghề nghiệp phụ huynh

  is_active: boolean;
  created_by: Types.ObjectId;
  updated_by: Types.ObjectId;
  deleted_at: Date;
  deleted_by: Types.ObjectId;
  created_at: Date;
}

interface ICustomerInfoDocument extends ICustomerInfo, Document {
  transform(): {};
}

interface ICustomerInfoModel extends Model<ICustomerInfoDocument> {
  objectOpts(): string[];
  get(id: string): any;
  getSlug(slug: string): any;
  list({
    page,
    perPage,
    sort,
    title,
    language,
    is_active,
  }: {
    page?: number;
    perPage?: number;
    sort?: string;
    title?: string;
    language?: string;
    is_active?: boolean;
  }): any;
  countItem({
    title, //
    language,
    is_active,
  }: {
    title?: string;
    language?: string;
    is_active?: boolean;
  }): any;
}

const customerInfoSchema = new Schema<ICustomerInfoDocument>(
  {
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    identify: {
      type: String,
    },
    cellular_network: {
      type: Schema.Types.ObjectId,
      ref: 'CustomerInfoOption',
    },
    data_created_at: {
      type: Date,
    },
    data_updated_at: {
      type: Date,
    },
    next_care_at: {
      type: Date,
    },
    advisor: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    channel: {
      type: Schema.Types.ObjectId,
      ref: 'CustomerInfoOption',
    },
    school: {
      type: Schema.Types.ObjectId,
      ref: 'CustomerInfoOption',
    },
    class: {
      type: String,
    },
    province: {
      type: Schema.Types.ObjectId,
      ref: 'CustomerInfoOption',
    },
    district: {
      type: Schema.Types.ObjectId,
      ref: 'CustomerInfoOption',
    },
    major_interest_most: {
      type: Schema.Types.ObjectId,
      ref: 'CustomerInfoOption',
    },
    major_interest: {
      type: String,
    },
    city_interest: {
      type: Schema.Types.ObjectId,
      ref: 'CustomerInfoOption',
    },
    level_interest: {
      type: Schema.Types.ObjectId,
      ref: 'CustomerInfoOption',
    },
    university_interest: {
      type: String,
    },
    data_source: {
      type: Schema.Types.ObjectId,
      ref: 'CustomerInfoOption',
    },
    introducer: {
      type: String,
    },
    is_know_ttu: {
      type: Boolean,
    },
    know_channel: {
      type: Schema.Types.ObjectId,
      ref: 'CustomerInfoOption',
    },
    is_apply_ttu: {
      type: Schema.Types.ObjectId,
      ref: 'CustomerInfoOption',
    },
    is_open_house: {
      type: Schema.Types.ObjectId,
      ref: 'CustomerInfoOption',
    },
    is_uni_prep: {
      type: Schema.Types.ObjectId,
      ref: 'CustomerInfoOption',
    },
    fee_reduction_type: {
      type: Schema.Types.ObjectId,
      ref: 'CustomerInfoOption',
    },
    fee_reduction_policy: {
      type: String,
    },
    admission_scholarship: {
      type: String,
    },
    talent_scholarship: {
      type: Boolean,
    },
    data_status: {
      type: Schema.Types.ObjectId,
      ref: 'CustomerInfoOption',
    },
    ttu_target: {
      type: Schema.Types.ObjectId,
      ref: 'CustomerInfoOption',
    },
    moet_target: {
      type: Schema.Types.ObjectId,
      ref: 'CustomerInfoOption',
    },
    why_ttu: {
      type: Schema.Types.ObjectId,
      ref: 'CustomerInfoOption',
    },
    why_not_ttu: {
      type: Schema.Types.ObjectId,
      ref: 'CustomerInfoOption',
    },
    note: {
      type: String,
    },
    education_type: {
      type: String,
      enum: objectOpts,
    },
    is_create_document: {
      type: Boolean,
      default: false,
    },
    //update
    private_candidate_note: {
      type: Schema.Types.ObjectId,
      ref: 'CustomerInfoOption',
    },
    ec: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    registration_number: {
      type: String,
    },
    consultation_phase: {
      type: Schema.Types.ObjectId,
      ref: 'CustomerInfoOption',
    },
    priority_area: {
      type: Schema.Types.ObjectId,
      ref: 'CustomerInfoOption',
    },
    family_background: {
      type: String,
    },
    parents_name: {
      type: String,
    },
    parents_number: {
      type: String,
    },
    parents_job: {
      type: String,
    },

    is_active: {
      type: Boolean,
      default: true,
    },
    created_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    updated_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    deleted_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    deleted_at: {
      type: Date,
    },
  },
  {
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  }
);

customerInfoSchema.methods.transform = function (): {} {
  const transformed: { [index: string]: any } = {};
  const fields = [
    '_id', //
    'user',
    'identify',
    'cellular_network',
    'data_created_at',
    'data_updated_at',
    'next_care_at',
    'advisor',
    'channel',
    'school',
    'class',
    'province',
    'district',
    'major_interest_most',
    'major_interest',
    'city_interest',
    'level_interest',
    'university_interest',
    'data_source',
    'introducer',
    'is_know_ttu',
    'know_channel',
    'is_apply_ttu',
    'is_open_house',
    'is_uni_prep',
    'fee_reduction_type',
    'fee_reduction_policy',
    'admission_scholarship',
    'talent_scholarship',
    'data_status',
    'ttu_target',
    'moet_target',
    'why_ttu',
    'why_not_ttu',
    'note',
    'education_type',
    'private_candidate_note',
    'ec',
    'registration_number',
    'priority_area',
    'family_background',
    'parents_name',
    'parents_number',
    'parents_job',
    'consultation_phase',

    'is_active',
    'created_at',
    'is_active',
    'delete_at',
  ];

  fields.forEach((field: any) => {
    transformed[field] = this[field];
  });

  return transformed;
};

customerInfoSchema.statics.list = function ({
  page = 1,
  perPage = 30,
  sort,
  is_active = true,
}: {
  page: number;
  perPage: number;
  sort: string;
  is_active: boolean;
}) {
  try {
    const options = omitBy(
      {
        is_active,
      },
      isNil
    );
    const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };
    const result = this.find(options).sort(sortOpts);
    if (perPage > -1) {
      result.skip(perPage * (page - 1)).limit(perPage);
    }
    return result.exec();
  } catch (error) {
    throw error;
  }
};

customerInfoSchema.statics.countItem = function ({
  key,
  is_active = true,
}: {
  key: string;
  is_active: boolean;
}) {
  try {
    const options = omitBy(
      {
        key,
        is_active,
      },
      isNil
    );
    return this.find(options).count().exec();
  } catch (error) {
    throw error;
  }
};

customerInfoSchema.statics.get = async function (id: string) {
  try {
    const info = await this.findById(id).exec();
    if (info) {
      return info;
    }
    throw new APIError({
      message: 'Info Option does not exist',
      status: httpStatus.NOT_FOUND,
    });
  } catch (error) {
    throw error;
  }
};

customerInfoSchema.statics.objectOpts = function () {
  return objectOpts;
};

customerInfoSchema.pre<ICustomerInfoDocument>('save', async function (next) {
  const doc = this;
  if (this.isModified('advisor')) {
    const user = await User.get(doc.user.toString());
    const advisor = await User.get(doc.advisor.toString());
    if (user && advisor) {
      await GmailService.sendAdvisorNotification({
        email: advisor.user.email,
        advisor_name: advisor.user.name,
        name: user.user.name,
        phone: user.user.phone,
      });
    }
  }
  next();
});

customerInfoSchema.post<ICustomerInfoDocument>('save', async function (doc, next) {
  try {
    await User.findOneAndUpdate(doc.user, { info: doc._id });
    next();
  } catch (error: any) {
    return next(error);
  }
});

const CustomerInfo = model<ICustomerInfoDocument, ICustomerInfoModel>(
  'CustomerInfo',
  customerInfoSchema
);

export default CustomerInfo;
