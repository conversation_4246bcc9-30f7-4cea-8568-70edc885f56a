// 4/3/2025
import <PERSON><PERSON> from 'joi';
import History from '../models/history.model';

const historyValidation = {
  list: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      is_active: Joi.boolean(),
      customer: Joi.string(),
      advisor: Joi.string(),
    }),
  },

  create: {
    body: Joi.object({
      customer: Joi.string().required(),
      advisor: Joi.string().required(),
      content: Joi.string(),
      tag: Joi.array().items(Joi.string().valid(...History.tagOpts())),
    }),
  },

  getOrDelete: {
    params: Joi.object({
      id: Joi.string()
        .regex(/^[a-fA-F0-9]{24}$/)
        .required(),
    }),
  },

  patch: {
    params: Joi.object({
      id: Joi.string()
        .regex(/^[a-fA-F0-9]{24}$/)
        .required(),
    }),
    body: Joi.object({
      customer: Joi.string(),
      advisor: Joi.string(),
      content: Joi.string().allow('').optional(),
      tag: Joi.array().items(Joi.string().valid(...History.tagOpts())),
    }),
  },
};

export default historyValidation;
