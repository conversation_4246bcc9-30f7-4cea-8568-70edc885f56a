// 16/4/2024
import { Request, Response, NextFunction } from 'express';
import BlockText from '../models/block.text.model';
import <PERSON><PERSON>r<PERSON><PERSON><PERSON> from '../../../middlewares/error';
import <PERSON><PERSON>andler from '../../../middlewares/success';

class BlockTextController {
  public static async load(req: any, res: Response, next: NextFunction, id: string) {
    try {
      const text = await BlockText.get(id);
      req.locals = { text };
      return next();
    } catch (error) {
      return ErrorHandler.handler(error, req, res);
    }
  }

  public static async get(req: any, res: Response, next: NextFunction) {
    try {
      SuccessHandler.success(req.locals.text.transform(), req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async remove(req: any, res: Response, next: NextFunction) {
    try {
      const { text } = req.locals;
      const now = new Date();

      const deleted = Object.assign(text, {
        deleted_by: req.user._id,
        deleted_at: now,
        is_active: false,
      });
      await deleted.save();
      SuccessHandler.success(deleted, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async list(req: Request, res: Response, next: NextFunction) {
    try {
      const texts = await BlockText.list(req.query);
      const count = await BlockText.countItem(req.query);
      const transformed = texts.map((text: any) => text.transform());
      SuccessHandler.success({ total: count, docs: transformed }, req, res);
    } catch (error: any) {
      next(error);
    }
  }

  public static async update(req: any, res: Response, next: NextFunction) {
    try {
      const { text } = req.locals;
      const { body, user } = req;
      const now = new Date();
      body.updated_by = user._id;
      body.updated_at = now;

      const updated = Object.assign(text, body);
      await updated.save();
      SuccessHandler.success(updated, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async create(req: any, res: Response, next: NextFunction) {
    try {
      const { body, user } = req;
      const now = new Date();
      body.created_at_by = user._id;
      body.created_at = now;
      const text = new BlockText(body);
      await text.save();
      SuccessHandler.success(text, req, res);
    } catch (error) {
      next(error);
    }
  }
}

export default BlockTextController;
