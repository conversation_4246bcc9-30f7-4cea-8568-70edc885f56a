import Joi from 'joi';
import User from '../models/user.model';
import CustomerInfo from '../models/customer.info.model';

const userValidation = {
  updateProfile: {
    body: Joi.object({
      phone: Joi.string().length(10),
      email: Joi.string().email(),
      name: Joi.string().max(128),
      avatar: Joi.string().allow('').optional(),
      user_name: Joi.string().min(3).max(25),
      gender: Joi.string().valid(...User.genders()),
      about: Joi.string().allow('').optional(),
      dial_code: Joi.string()
        .regex(new RegExp(/^\+\d+$/))
        .min(2),
      address: Joi.string().allow('').optional(),
      dob: Joi.date(),
    }),
  },

  create: {
    body: Joi.object({
      phone: Joi.string().length(10),
      email: Joi.string().email(),
      name: Joi.string().max(128),
      avatar: Joi.string().allow('').optional(),
      user_name: Joi.string().min(3).max(25),
      gender: Joi.string().valid(...User.genders()),
      about: Joi.string().allow('').optional(),
      dial_code: Joi.string()
        .regex(new RegExp(/^\+\d+$/))
        .min(2),
      address: Joi.string().allow('').optional(),
      dob: Joi.date(),
      role: Joi.string().valid(...User.roles()),
      password: Joi.string(),
    }),
  },

  admission: {
    body: Joi.object({
      phone: Joi.string().length(10).required(),
      email: Joi.string().email().required(),
      name: Joi.string().max(128).required(),
      dob: Joi.date().required(),
      identify: Joi.string().required(),
      gender: Joi.string()
        .valid(...User.genders())
        .required(),
      education_type: Joi.string()
        .valid(...CustomerInfo.objectOpts())
        .required(),
      major_interest_most: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
    }),
  },

  list: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      phone: Joi.string().length(10),
      email: Joi.string().email(),
      name: Joi.string().max(128),
      role: Joi.string().valid(...User.roles()),
      advisor: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
      education_type: Joi.string().valid(...CustomerInfo.objectOpts()),
      is_create_document: Joi.boolean(),
      gender: Joi.string().valid(...User.genders()),
    }),
  },

  getOrDelete: {
    params: Joi.object({
      id: Joi.string()
        .regex(/^[a-fA-F0-9]{24}$/)
        .required(),
    }),
  },
};

export default userValidation;
