// 4/3/2025
import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '../../../middlewares/error';
import Success<PERSON>andler from '../../../middlewares/success';
import History from '../models/history.model';
import APIError from '../../../utils/APIError';
import httpStatus from 'http-status';

class HistoryController {
  public static async load(req: any, res: Response, next: NextFunction, id: string) {
    try {
      const history = await History.get(id);
      req.locals = { history };
      return next();
    } catch (error) {
      return ErrorHandler.handler(error, req, res);
    }
  }

  public static async get(req: any, res: Response, next: NextFunction) {
    try {
      SuccessHandler.success(req.locals.history.transform(), req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async remove(req: any, res: Response, next: NextFunction) {
    try {
      const { history } = req.locals;
      const { user } = req;
      const now = new Date();
      if (
        user?.role &&
        (user.role === 'admin' || history.advisor.toString() === user._id.toString())
      ) {
        const deleted = Object.assign(history, {
          deleted_by: req.user._id,
          deleted_at: now,
          is_active: false,
        });
        await deleted.save();
        SuccessHandler.success(deleted, req, res);
      }else{
        throw new APIError({
          message: 'You don\'t have permission',
          status: httpStatus.FORBIDDEN,
        });
      }
     
    } catch (error) {
      next(error);
    }
  }

  public static async list(req: Request, res: Response, next: NextFunction) {
    try {
      const histories = await History.list(req.query);
      const count = await History.countItem(req.query);
      const transformed = histories.map((history: any) => history.transform());
      SuccessHandler.success({ total: count, docs: transformed }, req, res);
    } catch (error: any) {
      next(error);
    }
  }

  public static async update(req: any, res: Response, next: NextFunction) {
    try {
      const { history } = req.locals;
      const { body, user } = req;
      const now = new Date();
      body.updated_by = user._id;
      body.updated_at = now;

      const updated = Object.assign(history, body);
      await updated.save();
      SuccessHandler.success(updated, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async create(req: any, res: Response, next: NextFunction) {
    try {
      const { body, user } = req;
      const now = new Date();
      body.created_at_by = user._id;
      body.created_at = now;
      const history = new History(body);
      await history.save();
      SuccessHandler.success(history, req, res);
    } catch (error) {
      next(error);
    }
  }
}

export default HistoryController;
