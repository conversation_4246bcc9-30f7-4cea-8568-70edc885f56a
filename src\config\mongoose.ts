import mongoose from 'mongoose';
import bluebird from 'bluebird';
import Vars from './vars';

export class MongoDatabase {
  // Initialize your database pool
  public static init(): any {
    const dsn = Vars.config().mongoUri;
    const options = { keepAlive: true };

    (mongoose as any).Promise = bluebird;

    mongoose.connect(dsn, options, (error: any) => {
      // handle the error case
      if (error) {
        console.info('Failed to connect to the Mongo server!!');
        console.log(error);
        throw error;
      } else {
        console.info('connected to mongo server at: ' + dsn);
      }
    });
  }
}

export default mongoose;
