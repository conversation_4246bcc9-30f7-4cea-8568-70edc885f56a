// 15/5/2024

import <PERSON><PERSON> from 'joi';
import Article from '../models/article.model';

const articleValidation = {
  list: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      language: Joi.string().valid(...Article.langOpts()),
      is_active: Joi.boolean(),
      title: Joi.string(),
      category: Joi.string(),
    }),
  },

  create: {
    body: Joi.object({
      language: Joi.string().valid(...Article.langOpts()),
      title: Joi.string().required(),
      slug: Joi.string(),
      cover: Joi.string().allow('').optional(),
      content: Joi.string(),
      description: Joi.string().allow('').optional(),
      linked: Joi.array().items(Joi.string().regex(/^[a-fA-F0-9]{24}$/)),
      block: Joi.array().items(
        Joi.object({
          block_type: Joi.string().valid(...Article.blockTypeOpts()),
          block_id: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
        })
      ),
      tag: Joi.array().items(Joi.string()),
      category: Joi.array().items(Joi.string().regex(/^[a-fA-F0-9]{24}$/)),
    }),
  },

  getOrDelete: {
    params: Joi.object({
      id: Joi.string()
        .regex(/^[a-fA-F0-9]{24}$/)
        .required(),
    }),
  },

  patch: {
    params: Joi.object({
      id: Joi.string()
        .regex(/^[a-fA-F0-9]{24}$/)
        .required(),
    }),
    body: Joi.object({
      language: Joi.string().valid(...Article.langOpts()),
      title: Joi.string(),
      slug: Joi.string(),
      cover: Joi.string().allow('').optional(),
      content: Joi.string(),
      description: Joi.string().allow('').optional(),
      linked: Joi.array().items(Joi.string().regex(/^[a-fA-F0-9]{24}$/)),
      block: Joi.array().items(
        Joi.object({
          block_type: Joi.string().valid(...Article.blockTypeOpts()),
          block_id: Joi.string().regex(/^[a-fA-F0-9]{24}$/),
        })
      ),
      tag: Joi.array().items(Joi.string().allow('').optional()),
      category: Joi.array().items(Joi.string().regex(/^[a-fA-F0-9]{24}$/)),
    }),
  },
};

export default articleValidation;
