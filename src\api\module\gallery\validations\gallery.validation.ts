import Joi from 'joi';
import Gallery from '../models/gallery.model';

const formValidation = {
  list: {
    query: Joi.object({
      page: Joi.number().min(1),
      perPage: Joi.number().min(-1).max(100),
      type: Joi.string().valid(...Gallery.types()),
      name: Joi.string(),
    }),
  },
  getOrDelete: {
    params: Joi.object({
      id: Joi.string()
        .regex(/^[a-fA-F0-9]{24}$/)
        .required(),
    }),
  },
  update: {
    body: Joi.object({
      name: Joi.string(),
      description: Joi.string(),
    }),
    params: Joi.object({
      id: Joi.string()
        .regex(/^[a-fA-F0-9]{24}$/)
        .required(),
    }),
  },
};

export default formValidation;
