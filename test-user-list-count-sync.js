// Test script để kiểm tra đồng bộ giữa userSchema.statics.list và userSchema.statics.countItem
const axios = require('axios');

// Cấu hình base URL
const BASE_URL = 'http://localhost:3000/api/v1/users';

// Mock token
const AUTH_TOKEN = 'your-admin-token-here';

const headers = {
  'Authorization': `Bearer ${AUTH_TOKEN}`,
  'Content-Type': 'application/json'
};

/**
 * Test đồng bộ giữa list và count với các filter khác nhau
 */
async function testListCountSync() {
  console.log('\n🔄 Test: List vs Count Synchronization');
  console.log('=' .repeat(60));

  const testCases = [
    {
      name: 'No filters',
      params: {}
    },
    {
      name: 'Filter by name',
      params: { name: 'nguyen' }
    },
    {
      name: 'Filter by email',
      params: { email: '<EMAIL>' }
    },
    {
      name: 'Filter by role',
      params: { role: 'customer' }
    },
    {
      name: 'Filter by phone',
      params: { phone: '0123456789' }
    },
    {
      name: 'Filter by is_active',
      params: { is_active: true }
    },
    {
      name: 'Filter by gender',
      params: { gender: 'male' }
    },
    {
      name: 'Filter by education_type (info field)',
      params: { education_type: 'university' }
    },
    {
      name: 'Filter by advisor (info field)',
      params: { advisor: 'advisor_id_here' }
    },
    {
      name: 'Filter by is_create_document (info field)',
      params: { is_create_document: 'true' }
    },
    {
      name: 'Combined user fields',
      params: { 
        name: 'test',
        role: 'customer',
        is_active: true 
      }
    },
    {
      name: 'Combined info fields',
      params: { 
        education_type: 'university',
        advisor: 'advisor_id_here',
        is_create_document: 'true'
      }
    },
    {
      name: 'Mixed user and info fields',
      params: { 
        name: 'test',
        role: 'customer',
        education_type: 'university',
        is_create_document: 'false'
      }
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n📝 Testing: ${testCase.name}`);
    console.log(`   Params: ${JSON.stringify(testCase.params)}`);

    try {
      // Get list with pagination
      const listParams = {
        ...testCase.params,
        page: 1,
        perPage: 10
      };

      const listResponse = await axios.get(BASE_URL, {
        headers,
        params: listParams
      });

      // Get count
      const countResponse = await axios.get(`${BASE_URL}/count`, {
        headers,
        params: testCase.params
      });

      const listData = listResponse.data.data || [];
      const countData = countResponse.data.data || countResponse.data;
      const totalCount = typeof countData === 'number' ? countData : countData.total;

      console.log(`   📊 List returned: ${listData.length} items`);
      console.log(`   🔢 Count returned: ${totalCount}`);

      // Check if they make sense
      if (listData.length <= 10 && totalCount >= listData.length) {
        console.log(`   ✅ Sync OK: Count (${totalCount}) >= List items (${listData.length})`);
      } else if (listData.length === 10 && totalCount >= 10) {
        console.log(`   ✅ Sync OK: Count (${totalCount}) >= Page size (10)`);
      } else {
        console.log(`   ⚠️  Potential issue: Count (${totalCount}) vs List items (${listData.length})`);
      }

      // Test with larger page size to get more accurate comparison
      if (totalCount > 0 && totalCount <= 50) {
        const fullListResponse = await axios.get(BASE_URL, {
          headers,
          params: {
            ...testCase.params,
            page: 1,
            perPage: totalCount + 10 // Get all items
          }
        });

        const fullListData = fullListResponse.data.data || [];
        console.log(`   🔍 Full list: ${fullListData.length} items`);

        if (fullListData.length === totalCount) {
          console.log(`   ✅ Perfect sync: Count matches full list`);
        } else {
          console.log(`   ❌ Sync issue: Count (${totalCount}) != Full list (${fullListData.length})`);
        }
      }

    } catch (error) {
      console.log(`   ❌ Error: ${error.response?.data?.message || error.message}`);
    }
  }
}

/**
 * Test với MongoDB query trực tiếp
 */
function testDirectMongoComparison() {
  console.log('\n🗄️  MongoDB Query Comparison');
  console.log('=' .repeat(60));

  console.log('📝 List function logic:');
  console.log(`
// userSchema.statics.list
const options = omitBy({
  name: new RegExp(name || '', 'i'),
  email,
  role,
  phone,
  is_active,
  gender,
}, isNil);

const optionsInfo = omitBy({
  education_type,
  advisor,
  is_create_document,
}, isNil);

const result = this.find(options)
  .populate({
    path: 'info',
    select: '_id is_create_document education_type advisor',
    match: optionsInfo,
  })
  .sort(sortOpts);
  `);

  console.log('📝 Count function logic (updated):');
  console.log(`
// userSchema.statics.countItem (new implementation)
if (advisor || education_type || is_create_document) {
  // Use aggregation to match populate logic
  const pipeline = [
    { $match: options }, // User fields
    {
      $lookup: {
        from: 'customerinfos',
        localField: 'info',
        foreignField: '_id',
        as: 'info_detail',
      },
    },
    {
      $unwind: {
        path: '$info_detail',
        preserveNullAndEmptyArrays: true,
      },
    },
    { $match: infoMatchConditions }, // Info fields
    { $count: 'totalCount' },
  ];
} else {
  // Simple count for user fields only
  return this.find(options).countDocuments();
}
  `);

  console.log('✅ Key improvements:');
  console.log('   - Uses same filter logic as list function');
  console.log('   - Handles populate match conditions correctly');
  console.log('   - Separates user fields from info fields');
  console.log('   - Uses aggregation when info filters are present');
  console.log('   - Falls back to simple count when no info filters');
}

/**
 * Test edge cases
 */
async function testEdgeCases() {
  console.log('\n🔍 Test: Edge Cases');
  console.log('=' .repeat(60));

  const edgeCases = [
    {
      name: 'Empty string filters',
      params: { name: '', email: '', phone: '' }
    },
    {
      name: 'Boolean string filters',
      params: { is_create_document: 'true' }
    },
    {
      name: 'Boolean string false',
      params: { is_create_document: 'false' }
    },
    {
      name: 'Non-existent advisor',
      params: { advisor: 'non_existent_id' }
    },
    {
      name: 'Case insensitive name search',
      params: { name: 'NGUYEN' }
    },
    {
      name: 'Partial name search',
      params: { name: 'ng' }
    }
  ];

  for (const testCase of edgeCases) {
    console.log(`\n📝 Testing: ${testCase.name}`);

    try {
      const listResponse = await axios.get(BASE_URL, {
        headers,
        params: { ...testCase.params, page: 1, perPage: 5 }
      });

      const countResponse = await axios.get(`${BASE_URL}/count`, {
        headers,
        params: testCase.params
      });

      const listCount = listResponse.data.data?.length || 0;
      const totalCount = countResponse.data.data || 0;

      console.log(`   📊 List: ${listCount}, Count: ${totalCount}`);

      if (totalCount >= listCount) {
        console.log(`   ✅ Edge case handled correctly`);
      } else {
        console.log(`   ❌ Edge case issue detected`);
      }

    } catch (error) {
      console.log(`   ❌ Error: ${error.response?.data?.message || error.message}`);
    }
  }
}

/**
 * Performance comparison
 */
async function testPerformance() {
  console.log('\n⚡ Test: Performance Comparison');
  console.log('=' .repeat(60));

  const performanceTests = [
    {
      name: 'Simple user filter',
      params: { role: 'customer' }
    },
    {
      name: 'Info filter (uses aggregation)',
      params: { education_type: 'university' }
    },
    {
      name: 'Complex mixed filter',
      params: { 
        name: 'test',
        role: 'customer',
        education_type: 'university',
        is_create_document: 'true'
      }
    }
  ];

  for (const test of performanceTests) {
    console.log(`\n📊 Testing: ${test.name}`);

    // Test list performance
    const listStart = Date.now();
    try {
      await axios.get(BASE_URL, {
        headers,
        params: { ...test.params, page: 1, perPage: 50 }
      });
      const listDuration = Date.now() - listStart;
      console.log(`   📋 List query: ${listDuration}ms`);
    } catch (error) {
      console.log(`   ❌ List error: ${error.message}`);
    }

    // Test count performance
    const countStart = Date.now();
    try {
      await axios.get(`${BASE_URL}/count`, {
        headers,
        params: test.params
      });
      const countDuration = Date.now() - countStart;
      console.log(`   🔢 Count query: ${countDuration}ms`);
    } catch (error) {
      console.log(`   ❌ Count error: ${error.message}`);
    }
  }
}

/**
 * Chạy tất cả tests
 */
async function runAllTests() {
  console.log('🚀 User List-Count Synchronization Test Suite');
  console.log('=' .repeat(70));

  // Test 1: Basic synchronization
  await testListCountSync();

  // Test 2: MongoDB query comparison
  testDirectMongoComparison();

  // Test 3: Edge cases
  await testEdgeCases();

  // Test 4: Performance
  await testPerformance();

  console.log('\n🎉 All synchronization tests completed!');
  
  console.log('\n📋 Summary of Changes:');
  console.log('   ✅ countItem now uses same filter logic as list');
  console.log('   ✅ Proper handling of populate match conditions');
  console.log('   ✅ Aggregation pipeline for info field filters');
  console.log('   ✅ Simple count for user-only filters');
  console.log('   ✅ Consistent boolean handling');

  console.log('\n💡 Best Practices:');
  console.log('   - Always test list and count together');
  console.log('   - Use same filter logic in both functions');
  console.log('   - Handle populate conditions correctly');
  console.log('   - Consider performance implications');
  console.log('   - Test edge cases thoroughly');
}

// Chạy tests
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testListCountSync,
  testEdgeCases,
  testPerformance,
  runAllTests
};
