// 20/3/2024
import { Request, Response, NextFunction } from 'express';
import  Page  from '../models/page.model';
import <PERSON><PERSON>r<PERSON><PERSON><PERSON> from '../../../middlewares/error';
import SuccessHandler from '../../../middlewares/success';

class PageController {
  public static async load(req: any, res: Response, next: NextFunction, id: string) {
    try {
      const page = await Page.get(id);
      req.locals = { page };
      return next();
    } catch (error) {
      return ErrorHandler.handler(error, req, res);
    }
  }

  public static async get(req: any, res: Response, next: NextFunction) {
    try {
      SuccessHandler.success(req.locals.page.transform(), req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async getSlug(req: any, res: Response, next: NextFunction) {
    try {
      const { slug } = req.params;
      const page = await Page.getSlug(slug);
      SuccessHandler.success(page, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async remove(req: any, res: Response, next: NextFunction) {
    try {
      const { page } = req.locals;
      const now = new Date();

      const deleted = Object.assign(page, {
        deleted_by: req.user._id,
        deleted_at: now,
        is_active: false,
      });
      await deleted.save();
      SuccessHandler.success(deleted, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async list(req: Request, res: Response, next: NextFunction) {
    try {
      const pages = await Page.list(req.query);
      const count = await Page.countItem(req.query);
      const transformed = pages.map((page: any) => page.transform());
      SuccessHandler.success({ total: count, docs: transformed }, req, res);
    } catch (error: any) {
      next(error);
    }
  }

  public static async update(req: any, res: Response, next: NextFunction) {
    try {
      const { page } = req.locals;
      const { body, user } = req;
      const now = new Date();
      body.updated_by = user._id;
      body.updated_at = now;

      const updated = Object.assign(page, body);
      await updated.save();
      SuccessHandler.success(updated, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async create(req: any, res: Response, next: NextFunction) {
    try {
      const { body, user } = req;
      const now = new Date();
      body.created_at_by = user._id;
      body.created_at = now;
      const page = new Page(body);
      await page.save();
      SuccessHandler.success(page, req, res);
    } catch (error) {
      next(error);
    }
  }
}

export default PageController;
