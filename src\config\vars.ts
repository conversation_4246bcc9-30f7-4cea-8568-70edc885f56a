import 'dotenv/config';

class Vars {
  public static config(): any {
    const port = process.env.PORT || 3200;
    const uri = process.env.URI || `http://localhost:${port}`;
    const env = process.env.NODE_ENV || 'development';
    const logs = process.env.NODE_ENV === 'production' ? 'combined' : 'dev';
    const mongoUri =
      process.env.NODE_ENV === 'development' ? process.env.MONGO_URI_DEV : process.env.MONGO_URI;
    const jwtSecret = process.env.JWT_SECRET;
    const jwtExpirationInterval = process.env.JWT_EXPIRATION_MINUTES;
    const mail = {
      clientId: process.env.MAILER_CLIENT_ID,
      clientSecret: process.env.MAILER_CLIENT_SECRET,
      redirectUri: process.env.MAILER_REDIRECT_URI,
      refreshToken: process.env.MAILER_REFRESH_TOKEN,
      senderEmail: process.env.MAILER_EMAIL,
      applyNotificationAddress: process.env.MAILER_APPLY_NOTIFICATION_ADDRESS || '[]',
    };
    const uploadDir = {
      upload: process.env.UPLOAD_DIR,
      tmp: process.env.UPLOAD_TMP_DIR,
      gallery: process.env.UPLOAD_GALLERY,
      avatar: process.env.UPLOAD_AVATAR_DIR,
      post: process.env.UPLOAD_POST_DIR,
      post_content: process.env.UPLOAD_POST_CONTENT_DIR,
    };
    const mysql = {
      host: process.env.MYSQL_HOST,
      user: process.env.MYSQL_USER,
      password: process.env.MYSQL_PASSWORD,
      db: process.env.MYSQL_DB,
    };
    const staticUri = process.env.STATIC_URI;
    const googleSheets = {
      spreadsheetId: process.env.GOOGLE_SHEETS_SPREADSHEET_ID || '1GMhXSOJ3cUKxxRWdnSE6P9l78nBpTevsTpq4gcNlTqo',
      sheetName: process.env.GOOGLE_SHEETS_SHEET_NAME || 's1',
      credentialsPath: process.env.GOOGLE_SHEETS_CREDENTIALS_PATH || './src/credential/service-account.json',
      credentials: process.env.GOOGLE_SHEETS_CREDENTIALS || null,
      scopes: [
        'https://www.googleapis.com/auth/spreadsheets.readonly',
        'https://www.googleapis.com/auth/spreadsheets'
      ]
    };

    return {
      uri,
      port,
      env,
      logs,
      mongoUri,
      jwtSecret,
      jwtExpirationInterval,
      mail,
      uploadDir,
      staticUri,
      mysql,
      googleSheets,
    };
  }
}

export default Vars;
