import { google } from 'googleapis';
import * as fs from 'fs';
import * as path from 'path';
import Vars from './vars';

// Interface cho Google Sheets credentials
interface GoogleSheetsCredentials {
  type: string;
  project_id: string;
  private_key_id: string;
  private_key: string;
  client_email: string;
  client_id: string;
  auth_uri: string;
  token_uri: string;
  auth_provider_x509_cert_url: string;
  client_x509_cert_url: string;
}

// Interface cho Google Sheets config
interface GoogleSheetsConfig {
  spreadsheetId: string;
  sheetName: string;
  credentialsPath: string;
}

class SheetsWriter {
  private static instance: SheetsWriter;
  private auth: any;
  private sheets: any;
  private config: GoogleSheetsConfig;

  private constructor() {
    const { googleSheets } = Vars.config();

    this.config = {
      spreadsheetId: googleSheets.spreadsheetId,
      sheetName: googleSheets.sheetName,
      credentialsPath: googleSheets.credentialsPath
    };

    this.initializeAuth();
  }

  public static getInstance(): SheetsWriter {
    if (!SheetsWriter.instance) {
      SheetsWriter.instance = new SheetsWriter();
    }
    return SheetsWriter.instance;
  }

  private initializeAuth(): void {
    try {
      const { googleSheets } = Vars.config();
      let credentials: GoogleSheetsCredentials;

      if (googleSheets.credentials) {
        credentials = JSON.parse(googleSheets.credentials);
      } else {
        const credentialsPath = path.resolve(this.config.credentialsPath);
        if (!fs.existsSync(credentialsPath)) {
          throw new Error(`Google Sheets credentials file not found at: ${credentialsPath}`);
        }
        credentials = JSON.parse(fs.readFileSync(credentialsPath, 'utf-8'));
      }

      this.auth = new google.auth.GoogleAuth({
        credentials,
        scopes: googleSheets.scopes,
      });

      this.sheets = google.sheets({ version: 'v4', auth: this.auth });

      console.log('✅ Google Sheets Writer initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Google Sheets Writer:', error);
      throw error;
    }
  }

  /**
   * Ghi dữ liệu vào range cụ thể
   * @param range - Range để ghi (ví dụ: 'A1:C10')
   * @param values - Dữ liệu 2D array để ghi
   */
  public async writeToRange(range: string, values: any[][]): Promise<void> {
    try {
      await this.sheets.spreadsheets.values.update({
        spreadsheetId: this.config.spreadsheetId,
        range: `${this.config.sheetName}!${range}`,
        valueInputOption: 'RAW',
        requestBody: {
          values,
        },
      });
      console.log(`✅ Successfully wrote ${values.length} rows to range: ${range}`);
    } catch (error) {
      console.error('❌ Failed to write to Google Sheets:', error);
      throw error;
    }
  }

  /**
   * Thêm dữ liệu vào cuối sheet
   * @param values - Dữ liệu 2D array để thêm
   */
  public async appendData(values: any[][]): Promise<void> {
    try {
      await this.sheets.spreadsheets.values.append({
        spreadsheetId: this.config.spreadsheetId,
        range: `${this.config.sheetName}!A:Z`,
        valueInputOption: 'RAW',
        requestBody: {
          values,
        },
      });
      console.log(`✅ Successfully appended ${values.length} rows to sheet`);
    } catch (error) {
      console.error('❌ Failed to append data to Google Sheets:', error);
      throw error;
    }
  }

  /**
   * Xóa toàn bộ dữ liệu trong sheet và ghi dữ liệu mới
   * @param values - Dữ liệu 2D array để ghi
   */
  public async replaceAllData(values: any[][]): Promise<void> {
    try {
      // Xóa toàn bộ dữ liệu hiện tại
      await this.sheets.spreadsheets.values.clear({
        spreadsheetId: this.config.spreadsheetId,
        range: `${this.config.sheetName}!A:Z`,
      });

      // Ghi dữ liệu mới
      await this.sheets.spreadsheets.values.update({
        spreadsheetId: this.config.spreadsheetId,
        range: `${this.config.sheetName}!A1`,
        valueInputOption: 'RAW',
        requestBody: {
          values,
        },
      });
      console.log(`✅ Successfully replaced all data with ${values.length} rows`);
    } catch (error) {
      console.error('❌ Failed to replace data in Google Sheets:', error);
      throw error;
    }
  }

  /**
   * Kiểm tra kết nối và quyền ghi
   */
  public async testWriteAccess(): Promise<boolean> {
    try {
      // Test bằng cách ghi một cell test
      const testData = [['Test', new Date().toISOString()]];
      await this.sheets.spreadsheets.values.update({
        spreadsheetId: this.config.spreadsheetId,
        range: `${this.config.sheetName}!A1:B1`,
        valueInputOption: 'RAW',
        requestBody: {
          values: testData,
        },
      });
      console.log('✅ Write access confirmed');
      return true;
    } catch (error) {
      console.error('❌ No write access to Google Sheets:', error);
      return false;
    }
  }

  /**
   * Lấy Google Sheets API instance
   */
  public getSheets() {
    return this.sheets;
  }

  /**
   * Lấy thông tin cấu hình
   */
  public getConfig(): GoogleSheetsConfig {
    return { ...this.config };
  }

  /**
   * Cập nhật cấu hình
   */
  public updateConfig(newConfig: Partial<GoogleSheetsConfig>): void {
    this.config = { ...this.config, ...newConfig };
    if (newConfig.credentialsPath) {
      this.initializeAuth();
    }
  }
}

// Export singleton instance
export default SheetsWriter.getInstance();