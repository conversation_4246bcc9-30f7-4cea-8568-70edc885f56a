import { Strategy } from 'passport-local';
import User from '../../module/user/models/user.model';

class Local {
  public static init(passport: any): any {
    passport.use(
      new Strategy({ usernameField: 'email' }, (email, password, done) => {
        User.findOne({ email: email.toLowerCase() }, (err: any, user: any) => {
          if (err) {
            return done(err);
          }

          if (!user) {
            return done(null, false, { message: `E-mail ${email} not found.` });
          }

          if (user && !user.password) {
            return done(null, false, {
              message: `E-mail ${email} was not registered with us using any password. Please use the appropriate providers to Log-In again!`,
            });
          }

          user.comparePassword(password, (_err: any, _isMatch: any) => {
            if (_err) {
              return done(_err);
            }
            if (_isMatch) {
              return done(null, user);
            }
            return done(null, false, { message: 'Invalid E-mail or password.' });
          });
        });
      })
    );
  }
}

export default Local;
