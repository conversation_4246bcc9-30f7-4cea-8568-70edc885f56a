// 16/4/2024
import { Schema, model, Document, Model, Types } from 'mongoose';
import { omitBy, isNil } from 'lodash';
import APIError from '../../../utils/APIError';
import httpStatus from 'http-status';

interface IBlockText {
  order: number;
  title: string;
  content: string;

  is_active: boolean;
  created_by: Types.ObjectId;
  updated_by: Types.ObjectId;
  deleted_at: Date;
  deleted_by: Types.ObjectId;
}

interface IBlockTextDocument extends IBlockText, Document {
  transform(): {};
}

interface IBlockTextModel extends Model<IBlockTextDocument> {
  langOpts(): string[];
  get(id: string): any;
  list({
    page,
    perPage,
    sort,
    title,
    language,
    is_active,
  }: {
    page?: number;
    perPage?: number;
    sort?: string;
    title?: string;
    language?: string;
    is_active?: boolean;
  }): any;
  countItem({
    title, //
    language,
    is_active,
  }: {
    title?: string;
    language?: string;
    is_active?: boolean;
  }): any;
}

const blockTextSchema = new Schema<IBlockTextDocument>(
  {
    order: {
      type: Number,
    },
    title: {
      type: String,
      required: true,
      trim: true,
    },
    content: {
      type: String,
      trim: true,
    },

    is_active: {
      type: Boolean,
      default: true,
    },
    created_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    updated_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    deleted_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    deleted_at: {
      type: Date,
    },
  },
  {
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  }
);

blockTextSchema.methods.transform = function (): {} {
  const transformed: { [index: string]: any } = {};
  const fields = [
    '_id', //
    'title',
    'content',
    'order',

    'is_active',
    'created_at',
    'is_active',
    'delete_at',
  ];

  fields.forEach((field: any) => {
    transformed[field] = this[field];
  });

  return transformed;
};

blockTextSchema.statics.list = function ({
  page = 1,
  perPage = 30,
  sort,
  title,
  content,
  is_active = true,
}: {
  page: number;
  perPage: number;
  sort: string;
  title: string;
  content: string;
  is_active: boolean;
  is_parent: boolean;
}) {
  try {
    let options = omitBy(
      {
        title: new RegExp(title || '', 'i'),
        content: new RegExp(content || '', 'i'),
        is_active,
      },
      isNil
    );

    const sortOpts = sort ? JSON.parse(sort) : { order: 1 };
    const result = this.find(options).sort(sortOpts);
    if (perPage > -1) {
      result.skip(perPage * (page - 1)).limit(perPage);
    }
    return result.exec();
  } catch (error) {
    throw error;
  }
};

blockTextSchema.statics.countItem = function ({
  title,
  content,
  is_active = true,
}: {
  title: string;
  content: string;
  is_active: boolean;
}) {
  try {
    let options = omitBy(
      {
        title: new RegExp(title || '', 'i'),
        content: new RegExp(content || '', 'i'),
        is_active,
      },
      isNil
    );
    return this.find(options).count().exec();
  } catch (error) {
    throw error;
  }
};

blockTextSchema.statics.get = async function (id: string) {
  try {
    const blockCollapse = await this.findById(id).exec();
    if (blockCollapse) {
      return blockCollapse;
    }
    throw new APIError({
      message: 'BlockText does not exist',
      status: httpStatus.NOT_FOUND,
    });
  } catch (error) {
    throw error;
  }
};

const BlockText = model<IBlockTextDocument, IBlockTextModel>('BlockText', blockTextSchema);

export default BlockText;
