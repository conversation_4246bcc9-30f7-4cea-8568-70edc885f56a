// 20/3/2024
import { Schema, model, Document, Model, Types } from 'mongoose';
import { omitBy, isNil } from 'lodash';
import APIError from '../../../utils/APIError';
import httpStatus from 'http-status';
import Slug from 'slug';

const langOpts = ['vie', 'eng'];
const blockTypeOpts = ['BlockCollapse', 'BlockText'];

interface IBlock {
  block_type: string;
  block_id: Types.ObjectId;
}

interface IPage {
  title: string;
  slug: string;
  cover: string;
  content: string;
  description: string;
  language: string;
  linked: Types.ObjectId[];
  block: IBlock[];

  is_active: boolean;
  created_by: Types.ObjectId;
  updated_by: Types.ObjectId;
  deleted_at: Date;
  deleted_by: Types.ObjectId;
}

interface IPageDocument extends IPage, Document {
  transform(): {};
}

interface IPageModel extends Model<IPageDocument> {
  blockTypeOpts(): string[];
  langOpts(): string[];
  get(id: string): any;
  getSlug(slug: string): any;
  list({
    page,
    perPage,
    sort,
    title,
    language,
    is_active,
  }: {
    page?: number;
    perPage?: number;
    sort?: string;
    title?: string;
    language?: string;
    is_active?: boolean;
  }): any;
  countItem({
    title, //
    language,
    is_active,
  }: {
    title?: string;
    language?: string;
    is_active?: boolean;
  }): any;
}

const pageSchema = new Schema<IPageDocument>(
  {
    title: {
      type: String,
      required: true,
      maxlength: 128,
      trim: true,
    },
    slug: {
      type: String,
      trim: true,
      default: '',
    },
    cover: {
      type: String,
      trim: true,
      default: '',
    },
    content: {
      type: String,
      trim: true,
      default: '',
    },
    description: {
      type: String,
      trim: true,
      default: '',
    },
    language: {
      type: String,
      enum: langOpts,
      default: 'vie',
    },
    linked: [
      {
        type: Schema.Types.ObjectId,
        ref: 'Page',
      },
    ],
    block: [
      {
        block_type: {
          type: String,
          enum: blockTypeOpts,
        },
        block_id: {
          type: Schema.Types.ObjectId,
          refPath: 'block_type',
        },
      },
    ],

    is_active: {
      type: Boolean,
      default: true,
    },
    created_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    updated_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    deleted_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    deleted_at: {
      type: Date,
    },
  },
  {
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  }
);

pageSchema.methods.transform = function (): {} {
  const transformed: { [index: string]: any } = {};
  const fields = [
    '_id', //
    'title',
    'slug',
    'cover',
    'content',
    'description',
    'language',
    'linked',
    'block',
    'is_active',
    'created_at',
    'is_active',
    'delete_at',
  ];

  fields.forEach((field: any) => {
    transformed[field] = this[field];
  });

  return transformed;
};

pageSchema.statics.langOpts = () => langOpts;
pageSchema.statics.blockTypeOpts = () => blockTypeOpts;

pageSchema.statics.list = function ({
  page = 1,
  perPage = 30,
  sort,
  title,
  language,
  is_active = true,
}: {
  page: number;
  perPage: number;
  sort: string;
  title: string;
  language: string;
  is_active: boolean;
}) {
  try {
    const options = omitBy(
      {
        title: new RegExp(title || '', 'i'),
        language,
        is_active,
      },
      isNil
    );
    const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };
    const result = this.find(options).sort(sortOpts);
    if (perPage > -1) {
      result.skip(perPage * (page - 1)).limit(perPage);
    }
    return result.exec();
  } catch (error) {
    throw error;
  }
};

pageSchema.statics.countItem = function ({
  title,
  language,
  is_active = true,
}: {
  title: string;
  language: string;
  is_active: boolean;
}) {
  try {
    const options = omitBy(
      {
        title: new RegExp(title || '', 'i'),
        language,
        is_active,
      },
      isNil
    );
    return this.find(options).count().exec();
  } catch (error) {
    throw error;
  }
};

pageSchema.statics.get = async function (id: string) {
  try {
    const page = await this.findById(id)
      .populate([
        {
          path: 'linked',
          select: '_id language title slug',
        },
      ])
      .exec();
    if (page) {
      return page;
    }
    throw new APIError({
      message: 'Page does not exist',
      status: httpStatus.NOT_FOUND,
    });
  } catch (error) {
    throw error;
  }
};

pageSchema.statics.getSlug = async function (slug: string, is_active = true) {
  try {
    const page = await this.findOne({ slug, is_active }).exec();
    if (page) {
      return page;
    }
    throw new APIError({
      message: 'Page does not exist',
      status: httpStatus.NOT_FOUND,
    });
  } catch (error) {
    throw error;
  }
};

pageSchema.pre<IPageDocument>('save', async function (next) {
  const doc = this;
  if (this.isModified('title') && !doc.slug) {
    doc.slug = Slug(doc.title).toLowerCase();
  }
  if (this.isModified('linked')) {
    const currentPage = await Page.findById(doc._id);
    if (currentPage) {
      for (let j = 0; j < currentPage.linked.length; j += 1) {
        const item = currentPage.linked[j];
        await Page.updateOne({ _id: item }, { $pull: { linked: doc._id } });
      }
    }

    for (let i = 0; i < doc.linked.length; i += 1) {
      const item = doc.linked[i];
      await Page.updateOne({ _id: item }, { $push: { linked: doc._id } });
    }
  }
  next();
});

const Page = model<IPageDocument, IPageModel>('Page', pageSchema);

export default Page;
