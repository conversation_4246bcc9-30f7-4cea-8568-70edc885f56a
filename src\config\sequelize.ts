// import { Sequelize } from 'sequelize';
// import Vars from './vars';

// const mysql = Vars.config().mysql;

// // Khởi tạo Sequelize instance
// export const sequelize = new Sequelize(mysql.db, mysql.user, mysql.password, {
//   host: mysql.host,
//   dialect: 'mysql',
//   logging: false, // Bật log nếu cần: console.log
//   pool: {
//     max: 5,
//     min: 0,
//     acquire: 30000,
//     idle: 10000,
//   },
// });
