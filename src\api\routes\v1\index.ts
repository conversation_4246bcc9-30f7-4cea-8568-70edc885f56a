import express from 'express';
import Vars from '../../../config/vars';
import path from 'path';
import authRoute from '../../module/user/routes/v1/auth.route';
import userRoute from '../../module/user/routes/v1/user.route';
import pageRoute from '../../module/interface/routes/v1/page.route';
import menuRoute from '../../module/interface/routes/v1/menu.route';
import galleryRoute from '../../module/gallery/routes/v1/gallery.route';
import blockCollapseRoute from '../../module/interface/routes/v1/block.collapse.route';
import blockTextRoute from '../../module/interface/routes/v1/block.text.route';
import customerInfoOptionRoute from '../../module/user/routes/v1/customer.info.option.route';
import customerInfoRoute from '../../module/user/routes/v1/customer.info.route';
import articleRoute from '../../module/article/routes/v1/article.route';
import categoryRoute from '../../module/article/routes/v1/article.category.route';
import historyRoute from '../../module/user/routes/v1/history.route';
import admissionResultRoute from '../../module/admission.results/routes/v1/admission.results.route'

const { uploadDir } = Vars.config();
const router = express.Router();

router.get('/status', (req, res) => res.send('OK'));
router.use('/static', express.static(path.join(process.cwd(), uploadDir.upload)));

router.use('/auth', authRoute);
router.use('/user', userRoute);
router.use('/user/customer/info-option', customerInfoOptionRoute);
router.use('/user/customer/info', customerInfoRoute);
router.use('/user/customer/history', historyRoute);

router.use('/interface/page', pageRoute);
router.use('/interface/menu', menuRoute);
router.use('/interface/block-collapse', blockCollapseRoute);
router.use('/interface/block-text', blockTextRoute);
router.use('/gallery/gallery', galleryRoute);

router.use('/article/article', articleRoute);
router.use('/article/category', categoryRoute);

router.use('/admission/result', admissionResultRoute);

export default router;
