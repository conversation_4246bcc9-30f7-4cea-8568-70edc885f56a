import moment from 'moment';
import * as XLSX from 'xlsx';
import User from '../models/user.model';
import APIError from '../../../utils/APIError';
import httpStatus from 'http-status';
import <PERSON>rror<PERSON>andler from '../../../middlewares/error';
import { Request, Response, NextFunction } from 'express';
import SuccessHandler from '../../../middlewares/success';
import CustomerInfo from '../models/customer.info.model';
import Nodemailer from '../../../../config/mailer';
import CustomerInfoOption from '../models/customer.info.option.model';

class UserController {
  public static async loggedIn(req: any, res: Response) {
    SuccessHandler.success({ data: req.user.transform() }, req, res);
  }

  public static async load(req: any, res: Response, next: NextFunction, id: string) {
    try {
      const user = await User.get(id);
      req.locals = { user };
      return next();
    } catch (error) {
      return ErrorHandler.handler(error, req, res);
    }
  }

  public static async get(req: any, res: Response, next: NextFunction) {
    try {
      const user = req.locals.user.user.transform();
      let info = null;
      if (user.role === 'customer' && req.locals.user.info) {
        info = req.locals.user.info.transform();
      }
      SuccessHandler.success({ user, info }, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async patch(req: any, res: Response, next: NextFunction) {
    try {
      const user = req.locals.user.user;
      req.body.updated_by = req.user._id;
      const updatingUser = Object.assign(user, req.body);
      const updated = await updatingUser.save();
      SuccessHandler.success(updated, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async updateProfile(req: any, res: Response, next: NextFunction) {
    try {
      req.body.updated_by = req.user._id;
      const user = Object.assign(req.user, req.body);
      const updated = await user.save();
      SuccessHandler.success(updated, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async list(req: any, res: Response, next: NextFunction) {
    try {
      const users = await User.list(req.query);
      const count = await User.countItem(req.query);
      if (users[0] && typeof users[0].transform === 'function') {
        const transformed = users.map((user: any) => user.transform());
        SuccessHandler.success({ total: count, docs: transformed }, req, res);
      } else {
        SuccessHandler.success({ total: count, docs: users }, req, res);
      }
    } catch (error) {
      next(error);
    }
  }

  public static async create(req: any, res: Response, next: NextFunction) {
    try {
      const { body } = req;
      if (body.phone) {
        const phone = await User.count({ phone: body.phone, is_active: true });
        if (phone > 0) {
          throw new APIError({
            message: 'Validation Error',
            errors: [
              {
                field: 'phone',
                location: 'body',
                messages: ['"phone number" already exists'],
              },
            ],
            status: httpStatus.CONFLICT,
          });
        }
      }
      if (body.email) {
        const email = await User.count({ email: body.email, is_active: true });
        if (email > 0) {
          throw new APIError({
            message: 'Validation Error',
            errors: [
              {
                field: 'email',
                location: 'body',
                messages: ['"email" already exists'],
              },
            ],
            status: httpStatus.CONFLICT,
          });
        }
      }

      if (!body.password) {
        const password = User.generatePassword();
        body.password = password;
        // body.password_default = password;
      }
      const newCustomer = new User(body);
      await newCustomer.save();
      SuccessHandler.success(newCustomer, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async remove(req: any, res: Response, next: NextFunction) {
    try {
      const user = req.locals.user.user;
      const deleted = Object.assign(user, { is_active: false });
      await deleted.save();
      if (user.role === 'customer') {
        const now = new Date();
        await CustomerInfo.findOneAndUpdate(
          { user: user._id },
          { deleted_by: req.user._id, deleted_at: now, is_active: false }
        );
      }
      SuccessHandler.success(deleted, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async admission(req: any, res: Response, next: NextFunction) {
    try {
      const now = moment();
      const { body } = req;
      body.role = 'customer';
      if (body.phone) {
        const phone = await User.count({ phone: body.phone, is_active: true });
        if (phone > 0) {
          throw new APIError({
            message: 'Validation Error',
            errors: [
              {
                field: 'phone',
                location: 'body',
                messages: ['"phone number" already exists'],
              },
            ],
            status: httpStatus.CONFLICT,
          });
        }
      }
      if (body.email) {
        const email = await User.count({ email: body.email, is_active: true });
        if (email > 0) {
          throw new APIError({
            message: 'Validation Error',
            errors: [
              {
                field: 'email',
                location: 'body',
                messages: ['"email" already exists'],
              },
            ],
            status: httpStatus.CONFLICT,
          });
        }
      }

      if (!body.password) {
        const password = User.generatePassword();
        body.password = password;
        body.password_default = password;
      }
      const newCustomer = new User(body);
      await newCustomer.save();
      await CustomerInfo.findOneAndUpdate(
        { user: newCustomer._id },
        {
          identify: body.identify,
          education_type: body.education_type,
          major_interest_most: body.major_interest_most,
          data_created_at: now,
          data_source: '685268a1ad22c12a0c243ec6',
        }
      );

      const formattedTime = now.format('HH:mm DD/MM/YYYY');
      const major_interest_most = await CustomerInfoOption.findById(
        body.major_interest_most
      ).exec();

      await Nodemailer.sendApplyNotification({
        email: newCustomer.email,
        name: newCustomer.name,
        phone: newCustomer.phone,
        education_type: body.education_type,
        major_interest_most: major_interest_most?.value ?? '...',
        now: formattedTime,
      });

      SuccessHandler.success(
        {
          _id: newCustomer._id,
          email: newCustomer.email,
          phone: newCustomer.phone,
          name: newCustomer.name,
        },
        req,
        res
      );
    } catch (error) {
      next(error);
    }
  }

  public static async exportExcel(req: Request, res: Response, next: NextFunction) {
    try {
      const users = await User.listForExport();
      const data: any[] = [];
      for (let i = 0; i < users.length; i += 1) {
        const user = users[i];
        data.push({
          STT: i + 1,
          'Họ tên': user.name,
          SĐT: user.phone,
          Email: user.email,
          CCCD: user.info_detail?.identify ?? '',
          'Giới tính': user.gender === 'female' ? 'Nữ' : 'Nam',
          'Ngày sinh': user.dob,
          'Hệ đào tạo': user.info_detail?.education_type ?? '',
          'Ngày đăng ký': user.created_at,
        });
      }

      const worksheet = XLSX.utils.json_to_sheet(data);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');
      const buffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'buffer' });

      res.setHeader(
        'Content-Disposition',
        `attachment; filename="Danh-Sach-DK-Tuyen-Sinh[${moment()}].xlsx"`
      );
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      );
      res.send(buffer);
      // SuccessHandler.success(users, req, res);
    } catch (error) {
      next(error);
    }
  }
}
export default UserController;
