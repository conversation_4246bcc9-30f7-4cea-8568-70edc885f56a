// 25/3/2024
import { Request, Response, NextFunction } from 'express';
import <PERSON>u from '../models/menu.model';
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '../../../middlewares/error';
import <PERSON><PERSON>andler from '../../../middlewares/success';

class MenuController {
  public static async load(req: any, res: Response, next: NextFunction, id: string) {
    try {
      const menu = await Menu.get(id);
      req.locals = { menu };
      return next();
    } catch (error) {
      return ErrorHandler.handler(error, req, res);
    }
  }

  public static async get(req: any, res: Response, next: NextFunction) {
    try {
      SuccessHandler.success(req.locals.menu.transform(), req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async remove(req: any, res: Response, next: NextFunction) {
    try {
      const { menu } = req.locals;
      const now = new Date();

      const deleted = Object.assign(menu, {
        deleted_by: req.user._id,
        deleted_at: now,
        is_active: false,
      });
      await deleted.save();
      SuccessHandler.success(deleted, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async list(req: Request, res: Response, next: NextFunction) {
    try {
      const menus = await Menu.list(req.query);
      const count = await Menu.countItem(req.query);
      const transformed = menus.map((menu: any) => menu.transform());
      SuccessHandler.success({ total: count, docs: transformed }, req, res);
    } catch (error: any) {
      next(error);
    }
  }

  public static async update(req: any, res: Response, next: NextFunction) {
    try {
      const { menu } = req.locals;
      const { body, user } = req;
      const now = new Date();
      body.updated_by = user._id;
      body.updated_at = now;

      const updated = Object.assign(menu, body);
      await updated.save();
      SuccessHandler.success(updated, req, res);
    } catch (error) {
      next(error);
    }
  }

  public static async create(req: any, res: Response, next: NextFunction) {
    try {
      const { body, user } = req;
      const now = new Date();
      body.created_at_by = user._id;
      body.created_at = now;
      const menu = new Menu(body);
      await menu.save();
      SuccessHandler.success(menu, req, res);
    } catch (error) {
      next(error);
    }
  }
}

export default MenuController;
