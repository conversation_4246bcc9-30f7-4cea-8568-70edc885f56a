// 8/5/2024
import { Router } from 'express';
import { validate } from 'express-validation';
import controller from '../../controllers/customer.info.controller';
import validation from '../../validations/customer.info.validation';
import Auth from '../../../../middlewares/auth';

const router = Router();
router.param('id', controller.load);

router
  .route('/') //
  .get(validate(validation.list), controller.list)
  .post(
    Auth.authorize([...Auth.ADMIN, ...Auth.USER]),
    validate(validation.create),
    controller.create
  );

router
  .route('/import-from-excel') //
  .post(Auth.authorize(Auth.ADMIN), controller.importFromExcel);

router
  .route('/mapping-user') //
  .post(Auth.authorize(Auth.ADMIN), controller.mappingUser);

  router
  .route('/sync-data') //
  .post(Auth.authorize(Auth.ADMIN), controller.syncData);

router
  .route('/create-document') //
  .post(Auth.authorize([...Auth.ADMIN, ...Auth.USER]), controller.createDocument);

router
  .route('/:id') //
  .get(controller.get)
  .patch(
    Auth.authorize([...Auth.ADMIN, ...Auth.USER]),
    validate(validation.patch),
    controller.update
  )
  .delete(
    Auth.authorize([...Auth.ADMIN, ...Auth.USER]),
    validate(validation.getOrDelete),
    controller.remove
  );

export default router;
