import httpStatus from 'http-status';
import { ValidationError } from 'express-validation';
import APIError from '../utils/APIError';
import Vars from '../../config/vars';

const env = Vars.config().env;

class ErrorHandler {
  /**
   * Error handler. Send stacktrace only during development
   * @public
   */
  public static handler(err: any, req: any, res: any, next?: any) {
    if (err.message === 'Token expired' || err.message === 'Signature verification failed') {
      err.status = 401;
    }
    const response = {
      code: err.status,
      // message: err.message || httpStatus[err.status],
      message: err.message,
      errors: err.errors,
      stack: err.stack,
    };
    console.error('err: ', err);
    if (env !== 'development') {
      delete response.stack;
    }
    res.status(err.status);
    res.json(response);
    res.end();
  }

  /**
   * If error is not an instanceOf APIError, convert it.
   * @public
   */
  public static converter(err: any, req: any, res: any, next: any) {
    let convertedError = err;
    if (err instanceof ValidationError) {
      convertedError = new APIError({
        message: 'Validation Error',
        errors: err.details,
      });
    } else if (!(err instanceof APIError)) {
      convertedError = new APIError({
        message: err.message,
        status: err.status,
        stack: err.stack,
      });
    }

    return ErrorHandler.handler(convertedError, req, res);
  }

  /**
   * Catch 404 and forward to error handler
   * @public
   */
  public static notFound(req: any, res: any, next: any) {
    const err = new APIError({
      message: 'Not found',
      status: httpStatus.NOT_FOUND,
    });
    return ErrorHandler.handler(err, req, res);
  }
}

export default ErrorHandler;
