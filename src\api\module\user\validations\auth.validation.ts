import Joi from 'joi';
import User from '../models/user.model';

const authValidation = {
  register: {
    body: Joi.object({
      phone: Joi.string().length(10),
      email: Joi.string().email(),
      role: Joi.string().valid(...User.roles()),
      name: Joi.string().required().min(2).max(128),
      password: Joi.string().required().min(6).max(128),
    }),
  },
  sendActiveCode: {
    body: Joi.object({
      phone: Joi.string().length(10),
      email: Joi.string().email(),
      type: Joi.string().valid('email', 'phone').required(),
    }),
  },
};

export default authValidation;
