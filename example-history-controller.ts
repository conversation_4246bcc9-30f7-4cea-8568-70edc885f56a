// Example controller showing how to use the new date filter functionality
import { Request, Response, NextFunction } from 'express';
import History from '../src/api/module/user/models/history.model';
import SuccessHandler from '../src/api/middlewares/success';

/**
 * Example History Controller với date filtering
 */
class ExampleHistoryController {
  /**
   * <PERSON><PERSON><PERSON> danh sách history với date filter
   */
  public static async getHistoryList(req: Request, res: Response, next: NextFunction) {
    try {
      const {
        page = 1,
        perPage = 30,
        sort,
        customer,
        advisor,
        is_active = true,
        created_from,
        created_to,
      } = req.query;

      // Validate date parameters
      if (created_from && isNaN(Date.parse(created_from as string))) {
        return res.status(400).json({
          success: false,
          message: 'Invalid created_from date format. Use YYYY-MM-DD or ISO format.',
        });
      }

      if (created_to && isNaN(Date.parse(created_to as string))) {
        return res.status(400).json({
          success: false,
          message: 'Invalid created_to date format. Use YYYY-MM-DD or ISO format.',
        });
      }

      // Validate date range
      if (created_from && created_to) {
        const fromDate = new Date(created_from as string);
        const toDate = new Date(created_to as string);
        
        if (fromDate > toDate) {
          return res.status(400).json({
            success: false,
            message: 'created_from must be before or equal to created_to',
          });
        }

        // Optional: Limit date range to prevent performance issues
        const daysDiff = (toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24);
        if (daysDiff > 365) {
          return res.status(400).json({
            success: false,
            message: 'Date range cannot exceed 365 days',
          });
        }
      }

      // Get history list
      const histories = await History.list({
        page: Number(page),
        perPage: Number(perPage),
        sort: sort as string,
        customer: customer as string,
        advisor: advisor as string,
        is_active: is_active === 'true',
        created_from: created_from as string,
        created_to: created_to as string,
      });

      // Get total count
      const totalCount = await History.countItem({
        customer: customer as string,
        advisor: advisor as string,
        is_active: is_active === 'true',
        created_from: created_from as string,
        created_to: created_to as string,
      });

      const result = {
        histories,
        pagination: {
          page: Number(page),
          perPage: Number(perPage),
          total: totalCount,
          totalPages: Math.ceil(totalCount / Number(perPage)),
        },
        filters: {
          customer,
          advisor,
          is_active,
          created_from,
          created_to,
        },
      };

      SuccessHandler.success(result, req, res);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Lấy history hôm nay
   */
  public static async getTodayHistory(req: Request, res: Response, next: NextFunction) {
    try {
      const { customer, advisor } = req.query;
      const today = new Date().toISOString().split('T')[0];

      const histories = await History.list({
        page: 1,
        perPage: 100,
        customer: customer as string,
        advisor: advisor as string,
        is_active: true,
        created_from: today,
        created_to: today,
      });

      const result = {
        date: today,
        count: histories.length,
        histories,
      };

      SuccessHandler.success(result, req, res);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Lấy history theo tuần
   */
  public static async getWeeklyHistory(req: Request, res: Response, next: NextFunction) {
    try {
      const { customer, advisor, week_offset = 0 } = req.query;
      
      // Calculate week start and end
      const now = new Date();
      const weekStart = new Date(now.getTime() - (now.getDay() + Number(week_offset) * 7) * 24 * 60 * 60 * 1000);
      const weekEnd = new Date(weekStart.getTime() + 6 * 24 * 60 * 60 * 1000);

      const created_from = weekStart.toISOString().split('T')[0];
      const created_to = weekEnd.toISOString().split('T')[0];

      const histories = await History.list({
        page: 1,
        perPage: -1, // Get all records
        customer: customer as string,
        advisor: advisor as string,
        is_active: true,
        created_from,
        created_to,
      });

      // Group by day
      const dailyStats = {};
      histories.forEach(history => {
        const day = history.created_at.toISOString().split('T')[0];
        if (!dailyStats[day]) {
          dailyStats[day] = [];
        }
        dailyStats[day].push(history);
      });

      const result = {
        week: {
          start: created_from,
          end: created_to,
          offset: Number(week_offset),
        },
        totalCount: histories.length,
        dailyStats,
        histories,
      };

      SuccessHandler.success(result, req, res);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Lấy history theo tháng
   */
  public static async getMonthlyHistory(req: Request, res: Response, next: NextFunction) {
    try {
      const { customer, advisor, year, month } = req.query;
      
      const targetYear = year ? Number(year) : new Date().getFullYear();
      const targetMonth = month ? Number(month) - 1 : new Date().getMonth(); // Month is 0-based

      const monthStart = new Date(targetYear, targetMonth, 1);
      const monthEnd = new Date(targetYear, targetMonth + 1, 0);

      const created_from = monthStart.toISOString().split('T')[0];
      const created_to = monthEnd.toISOString().split('T')[0];

      const histories = await History.list({
        page: 1,
        perPage: -1,
        customer: customer as string,
        advisor: advisor as string,
        is_active: true,
        created_from,
        created_to,
      });

      // Group by day
      const dailyStats = {};
      histories.forEach(history => {
        const day = history.created_at.toISOString().split('T')[0];
        if (!dailyStats[day]) {
          dailyStats[day] = {
            count: 0,
            records: [],
          };
        }
        dailyStats[day].count++;
        dailyStats[day].records.push(history);
      });

      const result = {
        month: {
          year: targetYear,
          month: targetMonth + 1,
          start: created_from,
          end: created_to,
        },
        totalCount: histories.length,
        dailyStats,
        summary: {
          daysWithActivity: Object.keys(dailyStats).length,
          averagePerDay: Math.round(histories.length / new Date(targetYear, targetMonth + 1, 0).getDate() * 100) / 100,
        },
      };

      SuccessHandler.success(result, req, res);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Lấy thống kê history theo khoảng thời gian
   */
  public static async getHistoryStatistics(req: Request, res: Response, next: NextFunction) {
    try {
      const { created_from, created_to, customer, advisor } = req.query;

      if (!created_from || !created_to) {
        return res.status(400).json({
          success: false,
          message: 'Both created_from and created_to are required for statistics',
        });
      }

      const histories = await History.list({
        page: 1,
        perPage: -1,
        customer: customer as string,
        advisor: advisor as string,
        is_active: true,
        created_from: created_from as string,
        created_to: created_to as string,
      });

      // Calculate statistics
      const stats = {
        totalRecords: histories.length,
        dateRange: {
          from: created_from,
          to: created_to,
        },
        byTag: {},
        byAdvisor: {},
        byCustomer: {},
        timeline: {},
      };

      histories.forEach(history => {
        // By tag
        if (history.tag && history.tag.length > 0) {
          history.tag.forEach(tag => {
            stats.byTag[tag] = (stats.byTag[tag] || 0) + 1;
          });
        }

        // By advisor
        const advisorId = history.advisor?._id || history.advisor;
        if (advisorId) {
          if (!stats.byAdvisor[advisorId]) {
            stats.byAdvisor[advisorId] = {
              count: 0,
              name: history.advisor?.name || 'Unknown',
            };
          }
          stats.byAdvisor[advisorId].count++;
        }

        // By customer
        const customerId = history.customer?._id || history.customer;
        if (customerId) {
          if (!stats.byCustomer[customerId]) {
            stats.byCustomer[customerId] = {
              count: 0,
              name: history.customer?.name || 'Unknown',
            };
          }
          stats.byCustomer[customerId].count++;
        }

        // Timeline (by day)
        const day = history.created_at.toISOString().split('T')[0];
        stats.timeline[day] = (stats.timeline[day] || 0) + 1;
      });

      SuccessHandler.success(stats, req, res);
    } catch (error) {
      next(error);
    }
  }
}

export default ExampleHistoryController;
