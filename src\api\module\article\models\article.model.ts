// 14/5/2024
import { Schema, model, Document, Model, Types } from 'mongoose';
import { omitBy, isNil } from 'lodash';
import APIError from '../../../utils/APIError';
import httpStatus from 'http-status';
import Slug from 'slug';
import Category from './article.category.model';

const langOpts = ['vie', 'eng'];
const blockTypeOpts = ['BlockCollapse', 'BlockText'];

interface IBlock {
  block_type: string;
  block_id: Types.ObjectId;
}

interface IArticle {
  title: string;
  slug: string;
  cover: string;
  content: string;
  description: string;
  language: string;
  linked: Types.ObjectId[];
  block: IBlock[];
  tag: string[];
  category: Types.ObjectId[];

  is_active: boolean;
  created_by: Types.ObjectId;
  updated_by: Types.ObjectId;
  deleted_at: Date;
  deleted_by: Types.ObjectId;
}

interface IArticleDocument extends IArticle, Document {
  transform(): {};
}

interface IArticleModel extends Model<IArticleDocument> {
  blockTypeOpts(): string[];
  langOpts(): string[];
  get(id: string): any;
  getSlug(slug: string): any;
  list({
    page,
    perPage,
    sort,
    title,
    language,
    category,
    is_active,
  }: {
    page?: number;
    perPage?: number;
    sort?: string;
    title?: string;
    category?: string;
    language?: string;
    is_active?: boolean;
  }): any;
  countItem({
    title, //
    language,
    is_active,
  }: {
    title?: string;
    language?: string;
    is_active?: boolean;
  }): any;
}

const articleSchema = new Schema<IArticleDocument>(
  {
    title: {
      type: String,
      required: true,
      maxlength: 128,
      trim: true,
    },
    slug: {
      type: String,
      trim: true,
      default: '',
    },
    cover: {
      type: String,
      trim: true,
      default: '',
    },
    content: {
      type: String,
      trim: true,
      default: '',
    },
    description: {
      type: String,
      trim: true,
      default: '',
    },
    language: {
      type: String,
      enum: langOpts,
      default: 'vie',
    },
    linked: [
      {
        type: Schema.Types.ObjectId,
        ref: 'Article',
      },
    ],
    category: [
      {
        type: Schema.Types.ObjectId,
        ref: 'Category',
      },
    ],
    block: [
      {
        block_type: {
          type: String,
          enum: blockTypeOpts,
        },
        block_id: {
          type: Schema.Types.ObjectId,
          refPath: 'block_type',
        },
      },
    ],
    tag: [
      {
        type: String,
        trim: true,
      },
    ],

    is_active: {
      type: Boolean,
      default: true,
    },
    created_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    updated_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    deleted_by: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    deleted_at: {
      type: Date,
    },
  },
  {
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  }
);

articleSchema.methods.transform = function (): {} {
  const transformed: { [index: string]: any } = {};
  const fields = [
    '_id', //
    'title',
    'slug',
    'cover',
    'content',
    'description',
    'language',
    'linked',
    'block',
    'category',
    'is_active',
    'created_at',
    'is_active',
    'delete_at',
  ];

  fields.forEach((field: any) => {
    transformed[field] = this[field];
  });

  return transformed;
};

articleSchema.statics.langOpts = () => langOpts;
articleSchema.statics.blockTypeOpts = () => blockTypeOpts;

articleSchema.statics.list = async function ({
  page = 1,
  perPage = 30,
  sort,
  title,
  language,
  is_active = true,
  category,
}: {
  page: number;
  perPage: number;
  sort: string;
  title: string;
  language: string;
  is_active: boolean;
  category: string;
}) {
  try {
    const options = omitBy(
      {
        title: new RegExp(title || '', 'i'),
        language,
        is_active,
      },
      isNil
    );
    if (category) {
      const slug = await Category.findOne({ slug: category }).exec();
      if (slug) {
        options.category = slug._id;
      } else {
        return [];
      }
    }
    const sortOpts = sort ? JSON.parse(sort) : { created_at: -1 };
    const result = this.find(options)
      .populate({
        path: 'category',
        select: '_id language title slug',
      })
      .sort(sortOpts);
    if (perPage > -1) {
      result.skip(perPage * (page - 1)).limit(perPage);
    }
    return result.exec();
  } catch (error) {
    throw error;
  }
};

articleSchema.statics.countItem = async function ({
  title,
  language,
  is_active = true,
  category,
}: {
  title: string;
  language: string;
  is_active: boolean;
  category: string;
}) {
  try {
    const options = omitBy(
      {
        title: new RegExp(title || '', 'i'),
        language,
        is_active,
      },
      isNil
    );
    if (category) {
      const slug = await Category.findOne({ slug: category }).exec();
      if (slug) {
        options.category = slug._id;
      } else {
        return 0;
      }
    }
    return this.find(options).count().exec();
  } catch (error) {
    throw error;
  }
};

articleSchema.statics.get = async function (id: string) {
  try {
    const article = await this.findById(id)
      .populate([
        {
          path: 'linked',
          select: '_id language title slug',
        },
        {
          path: 'category',
          select: '_id language title slug',
        },
      ])
      .exec();
    if (article) {
      return article;
    }
    throw new APIError({
      message: 'Article does not exist',
      status: httpStatus.NOT_FOUND,
    });
  } catch (error) {
    throw error;
  }
};

articleSchema.statics.getSlug = async function (slug: string, is_active = true) {
  try {
    const article = await this.findOne({ slug, is_active })
      .populate([
        {
          path: 'linked',
          select: '_id language title slug',
        },
        {
          path: 'category',
          select: '_id language title slug',
        },
      ])
      .exec();
    if (article) {
      return article;
    }
    throw new APIError({
      message: 'Article does not exist',
      status: httpStatus.NOT_FOUND,
    });
  } catch (error) {
    throw error;
  }
};

articleSchema.pre<IArticleDocument>('save', async function (next) {
  const doc = this;
  if (this.isModified('title') && !doc.slug) {
    doc.slug = Slug(doc.title).toLowerCase();
  }
  if (this.isModified('linked')) {
    const currentArticle = await Article.findById(doc._id);
    if (currentArticle) {
      for (let j = 0; j < currentArticle.linked.length; j += 1) {
        const item = currentArticle.linked[j];
        await Article.updateOne({ _id: item }, { $pull: { linked: doc._id } });
      }
    }

    for (let i = 0; i < doc.linked.length; i += 1) {
      const item = doc.linked[i];
      await Article.updateOne({ _id: item }, { $push: { linked: doc._id } });
    }
  }
  next();
});

const Article = model<IArticleDocument, IArticleModel>('Article', articleSchema);

export default Article;
